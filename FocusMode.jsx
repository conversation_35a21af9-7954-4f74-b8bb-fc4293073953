import React, { useState, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX, Timer, Coffee, Waves, TreePine, CloudRain } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const ambientSounds = [
  {
    id: 'rain',
    name: 'Rain',
    icon: CloudRain,
    description: 'Gentle rainfall sounds',
    color: 'from-blue-500 to-cyan-400',
    // In a real app, this would be an actual audio file
    audioUrl: null
  },
  {
    id: 'forest',
    name: 'Forest',
    icon: TreePine,
    description: 'Birds chirping and leaves rustling',
    color: 'from-green-500 to-emerald-400',
    audioUrl: null
  },
  {
    id: 'waves',
    name: 'Ocean Waves',
    icon: Waves,
    description: 'Calming ocean waves',
    color: 'from-blue-600 to-teal-400',
    audioUrl: null
  },
  {
    id: 'cafe',
    name: 'Coffee Shop',
    icon: Coffee,
    description: 'Ambient cafe chatter',
    color: 'from-amber-500 to-orange-400',
    audioUrl: null
  }
];

const focusPlaylists = [
  {
    id: 1,
    name: 'Deep Focus',
    description: 'Instrumental tracks for deep concentration',
    trackCount: 45,
    duration: '2h 30m',
    color: 'from-purple-500 to-indigo-400'
  },
  {
    id: 2,
    name: 'Lo-fi Beats',
    description: 'Chill lo-fi hip hop for studying',
    trackCount: 60,
    duration: '3h 15m',
    color: 'from-pink-500 to-rose-400'
  },
  {
    id: 3,
    name: 'Classical Focus',
    description: 'Classical pieces for concentration',
    trackCount: 30,
    duration: '2h 45m',
    color: 'from-violet-500 to-purple-400'
  },
  {
    id: 4,
    name: 'Ambient Soundscapes',
    description: 'Atmospheric sounds for productivity',
    trackCount: 25,
    duration: '2h 10m',
    color: 'from-emerald-500 to-teal-400'
  }
];

export function FocusMode() {
  const [isActive, setIsActive] = useState(false);
  const [selectedSound, setSelectedSound] = useState(null);
  const [selectedPlaylist, setSelectedPlaylist] = useState(null);
  const [soundVolume, setSoundVolume] = useState([50]);
  const [musicVolume, setMusicVolume] = useState([70]);
  const [isSoundMuted, setIsSoundMuted] = useState(false);
  const [isMusicMuted, setIsMusicMuted] = useState(false);
  const [timer, setTimer] = useState(25); // Pomodoro timer in minutes
  const [timeLeft, setTimeLeft] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);

  // Pomodoro timer effect
  useEffect(() => {
    let interval = null;
    if (isTimerRunning && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(timeLeft => timeLeft - 1);
      }, 1000);
    } else if (timeLeft === 0 && isTimerRunning) {
      setIsTimerRunning(false);
      // In a real app, you would show a notification here
      console.log('Focus session completed!');
    }
    return () => clearInterval(interval);
  }, [isTimerRunning, timeLeft]);

  const startFocusSession = () => {
    setIsActive(true);
    setTimeLeft(timer * 60); // Convert minutes to seconds
    setIsTimerRunning(true);
  };

  const stopFocusSession = () => {
    setIsActive(false);
    setIsTimerRunning(false);
    setTimeLeft(0);
  };

  const toggleTimer = () => {
    setIsTimerRunning(!isTimerRunning);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleSoundSelect = (sound) => {
    setSelectedSound(selectedSound?.id === sound.id ? null : sound);
  };

  const handlePlaylistSelect = (playlist) => {
    setSelectedPlaylist(selectedPlaylist?.id === playlist.id ? null : playlist);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Focus Mode</h1>
          <p className="text-muted-foreground">Create the perfect environment for productivity</p>
        </div>
        
        {!isActive ? (
          <Button 
            onClick={startFocusSession}
            className="bg-green-500 hover:bg-green-600 text-white px-8 py-3 text-lg"
          >
            Start Focus Session
          </Button>
        ) : (
          <div className="flex items-center gap-4">
            <div className="text-center">
              <div className="text-3xl font-mono font-bold text-green-500">
                {formatTime(timeLeft)}
              </div>
              <div className="text-sm text-muted-foreground">Time remaining</div>
            </div>
            <Button 
              onClick={toggleTimer}
              variant="outline"
              className="px-6"
            >
              {isTimerRunning ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
              {isTimerRunning ? 'Pause' : 'Resume'}
            </Button>
            <Button 
              onClick={stopFocusSession}
              variant="destructive"
              className="px-6"
            >
              End Session
            </Button>
          </div>
        )}
      </div>

      {isActive && (
        <Card className="bg-green-500/10 border-green-500/20">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                <Timer className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-green-500">Focus Session Active</h3>
                <p className="text-sm text-muted-foreground">
                  Stay focused! You're doing great. {timeLeft > 0 && `${Math.ceil(timeLeft / 60)} minutes remaining.`}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="sounds" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="sounds">Ambient Sounds</TabsTrigger>
          <TabsTrigger value="music">Focus Music</TabsTrigger>
          <TabsTrigger value="timer">Timer Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="sounds" className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-4">Ambient Sounds</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {ambientSounds.map((sound) => {
                const Icon = sound.icon;
                const isSelected = selectedSound?.id === sound.id;
                
                return (
                  <Card 
                    key={sound.id} 
                    className={`cursor-pointer transition-all duration-200 hover:scale-105 ${
                      isSelected ? 'ring-2 ring-green-500 bg-green-500/10' : ''
                    }`}
                    onClick={() => handleSoundSelect(sound)}
                  >
                    <CardContent className="p-6 text-center">
                      <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br ${sound.color} flex items-center justify-center`}>
                        <Icon className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="font-semibold mb-2">{sound.name}</h3>
                      <p className="text-sm text-muted-foreground">{sound.description}</p>
                      {isSelected && (
                        <Badge className="mt-2 bg-green-500">Playing</Badge>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {selectedSound && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Volume2 className="h-5 w-5" />
                    Ambient Sound Volume
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsSoundMuted(!isSoundMuted)}
                      className="h-8 w-8 p-0"
                    >
                      {isSoundMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                    </Button>
                    <Slider
                      value={isSoundMuted ? [0] : soundVolume}
                      onValueChange={setSoundVolume}
                      max={100}
                      step={1}
                      className="flex-1"
                    />
                    <span className="text-sm text-muted-foreground w-12">
                      {isSoundMuted ? 0 : soundVolume[0]}%
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="music" className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-4">Focus Playlists</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {focusPlaylists.map((playlist) => {
                const isSelected = selectedPlaylist?.id === playlist.id;
                
                return (
                  <Card 
                    key={playlist.id} 
                    className={`cursor-pointer transition-all duration-200 hover:bg-muted/50 ${
                      isSelected ? 'ring-2 ring-green-500 bg-green-500/10' : ''
                    }`}
                    onClick={() => handlePlaylistSelect(playlist)}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-center gap-4">
                        <div className={`w-16 h-16 rounded-lg bg-gradient-to-br ${playlist.color} flex items-center justify-center`}>
                          <span className="text-white text-2xl">♪</span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold truncate">{playlist.name}</h3>
                          <p className="text-sm text-muted-foreground truncate">{playlist.description}</p>
                          <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                            <span>{playlist.trackCount} songs</span>
                            <span>•</span>
                            <span>{playlist.duration}</span>
                          </div>
                        </div>
                        {isSelected && (
                          <Badge className="bg-green-500">Playing</Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {selectedPlaylist && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Volume2 className="h-5 w-5" />
                    Music Volume
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsMusicMuted(!isMusicMuted)}
                      className="h-8 w-8 p-0"
                    >
                      {isMusicMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                    </Button>
                    <Slider
                      value={isMusicMuted ? [0] : musicVolume}
                      onValueChange={setMusicVolume}
                      max={100}
                      step={1}
                      className="flex-1"
                    />
                    <span className="text-sm text-muted-foreground w-12">
                      {isMusicMuted ? 0 : musicVolume[0]}%
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="timer" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Pomodoro Timer</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label className="text-sm font-medium mb-2 block">Focus Duration (minutes)</label>
                <Slider
                  value={[timer]}
                  onValueChange={(value) => setTimer(value[0])}
                  min={5}
                  max={120}
                  step={5}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-muted-foreground mt-2">
                  <span>5 min</span>
                  <span className="font-medium">{timer} minutes</span>
                  <span>2 hours</span>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <Button
                  variant={timer === 25 ? "default" : "outline"}
                  onClick={() => setTimer(25)}
                  className="h-12"
                >
                  25 min
                  <br />
                  <span className="text-xs opacity-70">Pomodoro</span>
                </Button>
                <Button
                  variant={timer === 45 ? "default" : "outline"}
                  onClick={() => setTimer(45)}
                  className="h-12"
                >
                  45 min
                  <br />
                  <span className="text-xs opacity-70">Deep Work</span>
                </Button>
                <Button
                  variant={timer === 90 ? "default" : "outline"}
                  onClick={() => setTimer(90)}
                  className="h-12"
                >
                  90 min
                  <br />
                  <span className="text-xs opacity-70">Flow State</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

