// Performance optimization utilities for Spotify 2.0
import { useEffect, useRef, useState, useCallback } from 'react';

// Debounce hook for search and input optimization
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Throttle hook for scroll and resize events
export const useThrottle = (callback, delay) => {
  const throttleRef = useRef(false);

  return useCallback((...args) => {
    if (!throttleRef.current) {
      callback(...args);
      throttleRef.current = true;
      setTimeout(() => {
        throttleRef.current = false;
      }, delay);
    }
  }, [callback, delay]);
};

// Intersection Observer hook for lazy loading
export const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const elementRef = useRef(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [hasIntersected, options]);

  return { elementRef, isIntersecting, hasIntersected };
};

// Virtual scrolling hook for large lists
export const useVirtualScroll = (items, itemHeight, containerHeight) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [visibleItems, setVisibleItems] = useState([]);

  useEffect(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );

    const visible = items.slice(startIndex, endIndex).map((item, index) => ({
      ...item,
      index: startIndex + index,
      top: (startIndex + index) * itemHeight
    }));

    setVisibleItems(visible);
  }, [items, itemHeight, containerHeight, scrollTop]);

  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);

  return {
    visibleItems,
    totalHeight: items.length * itemHeight,
    handleScroll
  };
};

// Image lazy loading component
export const LazyImage = ({ src, alt, className, placeholder, ...props }) => {
  const [imageSrc, setImageSrc] = useState(placeholder);
  const [isLoaded, setIsLoaded] = useState(false);
  const { elementRef, hasIntersected } = useIntersectionObserver();

  useEffect(() => {
    if (hasIntersected && src) {
      const img = new Image();
      img.onload = () => {
        setImageSrc(src);
        setIsLoaded(true);
      };
      img.src = src;
    }
  }, [hasIntersected, src]);

  return (
    <img
      ref={elementRef}
      src={imageSrc}
      alt={alt}
      className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-50'} transition-opacity duration-300`}
      {...props}
    />
  );
};

// Memoized component wrapper
export const memo = (Component, areEqual) => {
  return React.memo(Component, areEqual);
};

// Performance monitoring
export const performanceMonitor = {
  // Mark performance points
  mark: (name) => {
    if (performance.mark) {
      performance.mark(name);
    }
  },

  // Measure performance between marks
  measure: (name, startMark, endMark) => {
    if (performance.measure) {
      performance.measure(name, startMark, endMark);
    }
  },

  // Get performance entries
  getEntries: (type) => {
    if (performance.getEntriesByType) {
      return performance.getEntriesByType(type);
    }
    return [];
  },

  // Monitor Core Web Vitals
  observeWebVitals: (callback) => {
    // Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        callback('LCP', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          callback('FID', entry.processingStart - entry.startTime);
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        callback('CLS', clsValue);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    }
  }
};

// Bundle size analyzer
export const bundleAnalyzer = {
  // Analyze component bundle impact
  analyzeComponent: (componentName) => {
    const startTime = performance.now();
    
    return {
      start: () => {
        performanceMonitor.mark(`${componentName}-start`);
      },
      end: () => {
        performanceMonitor.mark(`${componentName}-end`);
        performanceMonitor.measure(
          `${componentName}-render`,
          `${componentName}-start`,
          `${componentName}-end`
        );
        
        const endTime = performance.now();
        console.log(`${componentName} render time: ${endTime - startTime}ms`);
      }
    };
  }
};

// Memory usage monitoring
export const memoryMonitor = {
  // Get current memory usage
  getUsage: () => {
    if (performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }
    return null;
  },

  // Monitor memory leaks
  startMonitoring: (interval = 5000) => {
    const monitor = setInterval(() => {
      const usage = memoryMonitor.getUsage();
      if (usage) {
        const usedMB = Math.round(usage.used / 1048576);
        const totalMB = Math.round(usage.total / 1048576);
        console.log(`Memory usage: ${usedMB}MB / ${totalMB}MB`);
        
        // Warn if memory usage is high
        if (usage.used / usage.limit > 0.8) {
          console.warn('High memory usage detected!');
        }
      }
    }, interval);

    return () => clearInterval(monitor);
  }
};

// Network optimization
export const networkOptimizer = {
  // Preload critical resources
  preloadResource: (href, as, type) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (type) link.type = type;
    document.head.appendChild(link);
  },

  // Prefetch next page resources
  prefetchResource: (href) => {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;
    document.head.appendChild(link);
  },

  // Check connection quality
  getConnectionInfo: () => {
    if (navigator.connection) {
      return {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt,
        saveData: navigator.connection.saveData
      };
    }
    return null;
  },

  // Adaptive loading based on connection
  shouldLoadHighQuality: () => {
    const connection = networkOptimizer.getConnectionInfo();
    if (!connection) return true;
    
    return connection.effectiveType === '4g' && 
           connection.downlink > 1.5 && 
           !connection.saveData;
  }
};

