import React, { useState, useEffect } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { usePlayer } from '@/contexts/PlayerContext';

// Mock lyrics data with timestamps (in seconds)
const mockLyrics = {
  1: { // Blinding Lights
    title: 'Blinding Lights',
    artist: 'The Weeknd',
    lines: [
      { time: 0, text: "Yeah" },
      { time: 2, text: "I've been tryna call" },
      { time: 5, text: "I've been on my own for long enough" },
      { time: 9, text: "Maybe you can show me how to love, maybe" },
      { time: 14, text: "I feel like I'm just missin'" },
      { time: 17, text: "Somethin' when you're gone" },
      { time: 20, text: "Somethin' in the way you move" },
      { time: 23, text: "Makes me feel like I can't live without you" },
      { time: 27, text: "It takes me all the way" },
      { time: 30, text: "I want you to stay" },
      { time: 33, text: "" },
      { time: 35, text: "I feel like I'm just missin'" },
      { time: 38, text: "Somethin' when you're gone" },
      { time: 41, text: "Somethin' in the way you move" },
      { time: 44, text: "Makes me feel like I can't live without you" },
      { time: 48, text: "It takes me all the way" },
      { time: 51, text: "I want you to stay" },
      { time: 54, text: "" },
      { time: 56, text: "I'm blinded by the lights" },
      { time: 60, text: "No, I can't sleep until I feel your touch" },
      { time: 64, text: "I said, ooh, I'm blinded by the lights" },
      { time: 68, text: "No, I can't sleep until I feel your touch" },
      { time: 72, text: "" },
      { time: 74, text: "I'm running out of time" },
      { time: 77, text: "'Cause I can see the sun light up the sky" },
      { time: 81, text: "So I hit the road in overdrive, baby, oh" },
      { time: 86, text: "" },
      { time: 88, text: "The city's cold and empty (Oh)" },
      { time: 92, text: "No one's around to judge me (Oh)" },
      { time: 96, text: "I can't see clearly when you're gone" },
      { time: 100, text: "" },
      { time: 102, text: "I said, ooh, I'm blinded by the lights" },
      { time: 106, text: "No, I can't sleep until I feel your touch" },
      { time: 110, text: "I said, ooh, I'm blinded by the lights" },
      { time: 114, text: "No, I can't sleep until I feel your touch" }
    ]
  },
  2: { // Watermelon Sugar
    title: 'Watermelon Sugar',
    artist: 'Harry Styles',
    lines: [
      { time: 0, text: "Tastes like strawberries on a summer evenin'" },
      { time: 4, text: "And it sounds just like a song" },
      { time: 8, text: "I want more berries and that summer feelin'" },
      { time: 12, text: "It's so wonderful and warm" },
      { time: 16, text: "" },
      { time: 18, text: "Breathe me in, breathe me out" },
      { time: 22, text: "I don't know if I could ever go without" },
      { time: 26, text: "I'm just thinking out loud" },
      { time: 30, text: "I don't know if I could ever go without" },
      { time: 34, text: "" },
      { time: 36, text: "Watermelon sugar high" },
      { time: 40, text: "Watermelon sugar high" },
      { time: 44, text: "Watermelon sugar high" },
      { time: 48, text: "Watermelon sugar high" },
      { time: 52, text: "Watermelon sugar" }
    ]
  }
};

export function LyricsDisplay({ className }) {
  const { currentTrack, currentTime, isPlaying } = usePlayer();
  const [currentLineIndex, setCurrentLineIndex] = useState(-1);
  const [lyrics, setLyrics] = useState(null);

  useEffect(() => {
    if (currentTrack && mockLyrics[currentTrack.id]) {
      setLyrics(mockLyrics[currentTrack.id]);
      setCurrentLineIndex(-1);
    } else {
      setLyrics(null);
      setCurrentLineIndex(-1);
    }
  }, [currentTrack]);

  useEffect(() => {
    if (!lyrics || !isPlaying) return;

    // Find the current line based on the current time
    let newLineIndex = -1;
    for (let i = lyrics.lines.length - 1; i >= 0; i--) {
      if (currentTime >= lyrics.lines[i].time) {
        newLineIndex = i;
        break;
      }
    }

    if (newLineIndex !== currentLineIndex) {
      setCurrentLineIndex(newLineIndex);
    }
  }, [currentTime, lyrics, isPlaying, currentLineIndex]);

  if (!currentTrack) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <div className="text-muted-foreground">
            <div className="text-4xl mb-4">🎵</div>
            <h3 className="text-lg font-semibold mb-2">No song playing</h3>
            <p>Start playing a song to see lyrics here</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!lyrics) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <div className="text-muted-foreground">
            <div className="text-4xl mb-4">📝</div>
            <h3 className="text-lg font-semibold mb-2">No lyrics available</h3>
            <p>Lyrics for "{currentTrack.title}" are not available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-0">
        {/* Header */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold">{lyrics.title}</h2>
              <p className="text-muted-foreground">{lyrics.artist}</p>
            </div>
            <Badge variant="secondary" className="bg-green-500/10 text-green-500">
              Synchronized
            </Badge>
          </div>
        </div>

        {/* Lyrics */}
        <ScrollArea className="h-96 p-6">
          <div className="space-y-4">
            {lyrics.lines.map((line, index) => {
              const isCurrent = index === currentLineIndex;
              const isPast = index < currentLineIndex;
              const isEmpty = line.text.trim() === '';

              return (
                <div
                  key={index}
                  className={`transition-all duration-300 ${
                    isEmpty 
                      ? 'h-4' 
                      : isCurrent
                      ? 'text-green-500 font-semibold text-lg scale-105 transform'
                      : isPast
                      ? 'text-muted-foreground/60'
                      : 'text-foreground/80'
                  }`}
                >
                  {!isEmpty && (
                    <p className="leading-relaxed">
                      {line.text}
                    </p>
                  )}
                </div>
              );
            })}
          </div>
        </ScrollArea>

        {/* Progress indicator */}
        <div className="p-4 border-t border-border">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>
              Line {Math.max(0, currentLineIndex + 1)} of {lyrics.lines.filter(l => l.text.trim() !== '').length}
            </span>
            <span>
              {Math.round((currentLineIndex + 1) / lyrics.lines.length * 100)}% complete
            </span>
          </div>
          <div className="w-full bg-muted rounded-full h-1 mt-2">
            <div 
              className="bg-green-500 h-1 rounded-full transition-all duration-300"
              style={{ 
                width: `${Math.max(0, (currentLineIndex + 1) / lyrics.lines.length * 100)}%` 
              }}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

