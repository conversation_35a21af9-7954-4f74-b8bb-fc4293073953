import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cloud, <PERSON>, <PERSON>ap, Coffee, Heart, Headphones } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { PlaylistCard } from '@/components/UI/PlaylistCard';
import { mockTracks } from '@/utils/mockData';

const moods = [
  {
    id: 'happy',
    name: 'Happy',
    icon: Sun,
    color: 'from-yellow-500 to-orange-400',
    description: 'Upbeat and energetic tracks',
    keywords: ['upbeat', 'energetic', 'positive', 'danceable']
  },
  {
    id: 'chill',
    name: 'Chill',
    icon: Cloud,
    color: 'from-blue-500 to-cyan-400',
    description: 'Relaxed and mellow vibes',
    keywords: ['chill', 'relaxed', 'mellow', 'ambient']
  },
  {
    id: 'focus',
    name: 'Focus',
    icon: Coffee,
    color: 'from-purple-500 to-indigo-400',
    description: 'Instrumental tracks for concentration',
    keywords: ['instrumental', 'focus', 'ambient', 'minimal']
  },
  {
    id: 'romantic',
    name: 'Romantic',
    icon: Heart,
    color: 'from-pink-500 to-rose-400',
    description: 'Love songs and romantic ballads',
    keywords: ['romantic', 'love', 'ballad', 'emotional']
  },
  {
    id: 'energetic',
    name: 'Energetic',
    icon: Zap,
    color: 'from-red-500 to-orange-400',
    description: 'High-energy workout tracks',
    keywords: ['energetic', 'workout', 'high-energy', 'motivational']
  },
  {
    id: 'nighttime',
    name: 'Nighttime',
    icon: Moon,
    color: 'from-indigo-500 to-purple-400',
    description: 'Perfect for late night listening',
    keywords: ['nighttime', 'dark', 'atmospheric', 'moody']
  }
];

const timeBasedMoods = {
  morning: ['happy', 'energetic', 'focus'],
  afternoon: ['focus', 'chill', 'happy'],
  evening: ['chill', 'romantic', 'nighttime'],
  night: ['nighttime', 'chill', 'romantic']
};

const weatherBasedMoods = {
  sunny: ['happy', 'energetic'],
  cloudy: ['chill', 'focus'],
  rainy: ['romantic', 'nighttime', 'chill'],
  stormy: ['energetic', 'focus']
};

export function MoodPlaylistGenerator() {
  const [selectedMood, setSelectedMood] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedPlaylist, setGeneratedPlaylist] = useState(null);
  const [progress, setProgress] = useState(0);
  const [suggestedMoods, setSuggestedMoods] = useState([]);

  useEffect(() => {
    // Generate mood suggestions based on time of day
    const hour = new Date().getHours();
    let timeOfDay;
    
    if (hour >= 6 && hour < 12) timeOfDay = 'morning';
    else if (hour >= 12 && hour < 17) timeOfDay = 'afternoon';
    else if (hour >= 17 && hour < 22) timeOfDay = 'evening';
    else timeOfDay = 'night';

    const suggestedMoodIds = timeBasedMoods[timeOfDay];
    const suggested = moods.filter(mood => suggestedMoodIds.includes(mood.id));
    setSuggestedMoods(suggested);
  }, []);

  const generatePlaylist = async (mood) => {
    setSelectedMood(mood);
    setIsGenerating(true);
    setProgress(0);

    // Simulate AI playlist generation with progress
    const steps = [
      'Analyzing your mood...',
      'Finding matching tracks...',
      'Optimizing track order...',
      'Adding variety...',
      'Finalizing playlist...'
    ];

    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 800));
      setProgress((i + 1) * 20);
    }

    // Generate a mock playlist based on the mood
    const playlistTracks = mockTracks.filter((_, index) => index < 15); // Take first 15 tracks
    const playlist = {
      id: `mood_${mood.id}_${Date.now()}`,
      name: `${mood.name} Mix`,
      description: `AI-generated playlist for your ${mood.name.toLowerCase()} mood`,
      trackCount: playlistTracks.length,
      creator: 'Spotify AI',
      coverArt: null,
      isPublic: false,
      isCollaborative: false,
      isOwner: true,
      tracks: playlistTracks.map(t => t.id),
      mood: mood,
      generatedAt: new Date().toISOString()
    };

    setGeneratedPlaylist(playlist);
    setIsGenerating(false);
    setProgress(100);
  };

  const savePlaylist = () => {
    if (generatedPlaylist) {
      // In a real app, this would save to the user's library
      console.log('Saving playlist:', generatedPlaylist);
      alert('Playlist saved to your library!');
    }
  };

  const regeneratePlaylist = () => {
    if (selectedMood) {
      generatePlaylist(selectedMood);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Mood-Based Playlists</h1>
        <p className="text-muted-foreground">
          Let AI create the perfect playlist based on your current mood, time of day, or weather
        </p>
      </div>

      {/* Suggested Moods */}
      {suggestedMoods.length > 0 && !generatedPlaylist && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-yellow-500" />
              Suggested for You
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {suggestedMoods.map((mood) => {
                const Icon = mood.icon;
                return (
                  <Card 
                    key={mood.id}
                    className="cursor-pointer hover:scale-105 transition-transform"
                    onClick={() => generatePlaylist(mood)}
                  >
                    <CardContent className="p-4 text-center">
                      <div className={`w-12 h-12 mx-auto mb-3 rounded-full bg-gradient-to-br ${mood.color} flex items-center justify-center`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="font-semibold">{mood.name}</h3>
                      <p className="text-sm text-muted-foreground mt-1">{mood.description}</p>
                      <Badge variant="secondary" className="mt-2">
                        Recommended
                      </Badge>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* All Moods */}
      {!generatedPlaylist && (
        <div>
          <h2 className="text-xl font-semibold mb-4">Choose Your Mood</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {moods.map((mood) => {
              const Icon = mood.icon;
              return (
                <Card 
                  key={mood.id}
                  className="cursor-pointer hover:scale-105 transition-transform"
                  onClick={() => generatePlaylist(mood)}
                >
                  <CardContent className="p-6 text-center">
                    <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br ${mood.color} flex items-center justify-center`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="font-semibold mb-2">{mood.name}</h3>
                    <p className="text-xs text-muted-foreground">{mood.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Generation Progress */}
      {isGenerating && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="space-y-4">
              <div className="w-16 h-16 mx-auto bg-gradient-to-br from-green-500 to-emerald-400 rounded-full flex items-center justify-center animate-pulse">
                <Sparkles className="h-8 w-8 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">Creating Your Perfect Playlist</h3>
                <p className="text-muted-foreground mb-4">
                  AI is analyzing your {selectedMood?.name.toLowerCase()} mood and finding the best tracks...
                </p>
                <Progress value={progress} className="w-full max-w-md mx-auto" />
                <p className="text-sm text-muted-foreground mt-2">{progress}% complete</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Generated Playlist */}
      {generatedPlaylist && !isGenerating && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-green-500" />
                Your Playlist is Ready!
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-6">
                <div className="w-32 h-32 bg-gradient-to-br from-green-500 to-emerald-400 rounded-lg flex items-center justify-center">
                  <Headphones className="h-16 w-16 text-white" />
                </div>
                <div className="flex-1">
                  <h2 className="text-2xl font-bold mb-2">{generatedPlaylist.name}</h2>
                  <p className="text-muted-foreground mb-4">{generatedPlaylist.description}</p>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                    <span>{generatedPlaylist.trackCount} songs</span>
                    <span>•</span>
                    <span>Generated by AI</span>
                    <span>•</span>
                    <Badge variant="secondary" className={`bg-gradient-to-r ${selectedMood?.color} text-white`}>
                      {selectedMood?.name}
                    </Badge>
                  </div>
                  <div className="flex gap-3">
                    <Button 
                      onClick={savePlaylist}
                      className="bg-green-500 hover:bg-green-600"
                    >
                      Save to Library
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={regeneratePlaylist}
                    >
                      Generate New Mix
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setGeneratedPlaylist(null)}
                    >
                      Try Different Mood
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Playlist Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Playlist Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {mockTracks.slice(0, 5).map((track, index) => (
                  <div key={track.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50">
                    <span className="text-sm text-muted-foreground w-6">{index + 1}</span>
                    <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-400 rounded-md" />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{track.title}</p>
                      <p className="text-sm text-muted-foreground truncate">{track.artist}</p>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {Math.floor(track.duration / 60)}:{(track.duration % 60).toString().padStart(2, '0')}
                    </span>
                  </div>
                ))}
                {generatedPlaylist.trackCount > 5 && (
                  <div className="text-center py-4 text-muted-foreground">
                    ... and {generatedPlaylist.trackCount - 5} more songs
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

