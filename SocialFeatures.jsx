import React, { useState } from 'react';
import { Share2, Users, MessageCircle, Heart, Copy, Twitter, Facebook, Instagram, Link } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { usePlayer } from '@/contexts/PlayerContext';

const mockFriends = [
  {
    id: 1,
    name: '<PERSON>',
    username: '@alice_music',
    avatar: null,
    isOnline: true,
    currentlyPlaying: 'Blinding Lights - The Weeknd'
  },
  {
    id: 2,
    name: '<PERSON>',
    username: '@bobbeats',
    avatar: null,
    isOnline: false,
    lastSeen: '2 hours ago'
  },
  {
    id: 3,
    name: 'Carol Davis',
    username: '@carol_vibes',
    avatar: null,
    isOnline: true,
    currentlyPlaying: 'Watermelon Sugar - Harry Styles'
  }
];

const mockActivity = [
  {
    id: 1,
    user: 'Alice Johnson',
    action: 'shared a song',
    content: 'Blinding Lights by The Weeknd',
    time: '5 minutes ago',
    likes: 12,
    comments: 3
  },
  {
    id: 2,
    user: 'Bob Smith',
    action: 'created a playlist',
    content: 'Summer Vibes 2024',
    time: '1 hour ago',
    likes: 8,
    comments: 1
  },
  {
    id: 3,
    user: 'Carol Davis',
    action: 'liked a playlist',
    content: 'Chill Indie Rock',
    time: '2 hours ago',
    likes: 5,
    comments: 0
  }
];

export function SocialFeatures() {
  const { currentTrack } = usePlayer();
  const [shareMessage, setShareMessage] = useState('');
  const [collaborators, setCollaborators] = useState([]);
  const [newCollaborator, setNewCollaborator] = useState('');

  const handleShare = (platform) => {
    if (!currentTrack) return;

    const shareText = shareMessage || `Check out "${currentTrack.title}" by ${currentTrack.artist} on Spotify 2.0!`;
    const shareUrl = `https://spotify2.app/track/${currentTrack.id}`;

    switch (platform) {
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`);
        break;
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`);
        break;
      case 'instagram':
        // Instagram doesn't support direct sharing, so copy to clipboard
        navigator.clipboard.writeText(`${shareText} ${shareUrl}`);
        alert('Link copied to clipboard! You can now paste it on Instagram.');
        break;
      case 'copy':
        navigator.clipboard.writeText(`${shareText} ${shareUrl}`);
        alert('Link copied to clipboard!');
        break;
      default:
        break;
    }
  };

  const addCollaborator = () => {
    if (newCollaborator.trim()) {
      const newCollab = {
        id: Date.now(),
        name: newCollaborator,
        username: `@${newCollaborator.toLowerCase().replace(' ', '_')}`,
        avatar: null,
        role: 'editor'
      };
      setCollaborators([...collaborators, newCollab]);
      setNewCollaborator('');
    }
  };

  const removeCollaborator = (id) => {
    setCollaborators(collaborators.filter(c => c.id !== id));
  };

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Social Features</h1>
        <p className="text-muted-foreground">
          Share your music, collaborate with friends, and discover what others are listening to
        </p>
      </div>

      <Tabs defaultValue="share" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="share">Share Music</TabsTrigger>
          <TabsTrigger value="friends">Friends</TabsTrigger>
          <TabsTrigger value="collaborate">Collaborate</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="share" className="space-y-6">
          {/* Current Track Sharing */}
          {currentTrack ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Share2 className="h-5 w-5" />
                  Share Current Track
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-400 rounded-lg" />
                  <div>
                    <h3 className="font-semibold">{currentTrack.title}</h3>
                    <p className="text-muted-foreground">{currentTrack.artist}</p>
                    <p className="text-sm text-muted-foreground">{currentTrack.album}</p>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Add a message (optional)</label>
                  <Textarea
                    placeholder="What do you think about this track?"
                    value={shareMessage}
                    onChange={(e) => setShareMessage(e.target.value)}
                    className="resize-none"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <Button
                    onClick={() => handleShare('twitter')}
                    className="bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    <Twitter className="h-4 w-4 mr-2" />
                    Twitter
                  </Button>
                  <Button
                    onClick={() => handleShare('facebook')}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <Facebook className="h-4 w-4 mr-2" />
                    Facebook
                  </Button>
                  <Button
                    onClick={() => handleShare('instagram')}
                    className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
                  >
                    <Instagram className="h-4 w-4 mr-2" />
                    Instagram
                  </Button>
                  <Button
                    onClick={() => handleShare('copy')}
                    variant="outline"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Link
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Share2 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No track playing</h3>
                <p className="text-muted-foreground">Start playing a song to share it with your friends</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="friends" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Friends Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockFriends.map((friend) => (
                  <div key={friend.id} className="flex items-center gap-4 p-3 rounded-lg hover:bg-muted/50">
                    <div className="relative">
                      <Avatar>
                        <AvatarImage src={friend.avatar} />
                        <AvatarFallback>{friend.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                      </Avatar>
                      {friend.isOnline && (
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-background" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold">{friend.name}</h3>
                        <span className="text-sm text-muted-foreground">{friend.username}</span>
                      </div>
                      {friend.isOnline ? (
                        friend.currentlyPlaying ? (
                          <p className="text-sm text-green-500">🎵 {friend.currentlyPlaying}</p>
                        ) : (
                          <p className="text-sm text-green-500">Online</p>
                        )
                      ) : (
                        <p className="text-sm text-muted-foreground">Last seen {friend.lastSeen}</p>
                      )}
                    </div>
                    <Button variant="outline" size="sm">
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Message
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="collaborate" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Collaborative Playlists
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Add Collaborator</label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Enter friend's name or username"
                    value={newCollaborator}
                    onChange={(e) => setNewCollaborator(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addCollaborator()}
                  />
                  <Button onClick={addCollaborator}>Add</Button>
                </div>
              </div>

              {collaborators.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3">Current Collaborators</h4>
                  <div className="space-y-2">
                    {collaborators.map((collaborator) => (
                      <div key={collaborator.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Avatar className="w-8 h-8">
                            <AvatarFallback className="text-xs">
                              {collaborator.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-sm">{collaborator.name}</p>
                            <p className="text-xs text-muted-foreground">{collaborator.username}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">{collaborator.role}</Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeCollaborator(collaborator.id)}
                          >
                            Remove
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <h4 className="font-medium text-blue-500 mb-2">Collaboration Features</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Real-time playlist editing</li>
                  <li>• Add/remove songs together</li>
                  <li>• See who added what</li>
                  <li>• Chat while listening</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="h-5 w-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-4 p-3 rounded-lg hover:bg-muted/50">
                    <Avatar>
                      <AvatarFallback>{activity.user.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm">
                        <span className="font-medium">{activity.user}</span> {activity.action}{' '}
                        <span className="font-medium">{activity.content}</span>
                      </p>
                      <div className="flex items-center gap-4 mt-2">
                        <span className="text-xs text-muted-foreground">{activity.time}</span>
                        <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                          <Heart className="h-3 w-3 mr-1" />
                          {activity.likes}
                        </Button>
                        <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                          <MessageCircle className="h-3 w-3 mr-1" />
                          {activity.comments}
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

