// Local storage utilities for Spotify 2.0
export const storage = {
  // Generic storage methods
  set: (key, value) => {
    try {
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(`spotify2_${key}`, serializedValue);
      return true;
    } catch (error) {
      console.error('Error saving to localStorage:', error);
      return false;
    }
  },

  get: (key, defaultValue = null) => {
    try {
      const item = localStorage.getItem(`spotify2_${key}`);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return defaultValue;
    }
  },

  remove: (key) => {
    try {
      localStorage.removeItem(`spotify2_${key}`);
      return true;
    } catch (error) {
      console.error('Error removing from localStorage:', error);
      return false;
    }
  },

  clear: () => {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith('spotify2_'));
      keys.forEach(key => localStorage.removeItem(key));
      return true;
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      return false;
    }
  },

  // User preferences
  getUserPreferences: () => {
    return storage.get('user_preferences', {
      theme: 'dark',
      volume: 0.7,
      quality: 'high',
      autoplay: true,
      notifications: true,
      language: 'en',
      crossfade: false,
      gaplessPlayback: true
    });
  },

  setUserPreferences: (preferences) => {
    const current = storage.getUserPreferences();
    const updated = { ...current, ...preferences };
    return storage.set('user_preferences', updated);
  },

  // Recently played tracks
  getRecentlyPlayed: () => {
    return storage.get('recently_played', []);
  },

  addToRecentlyPlayed: (track) => {
    const recent = storage.getRecentlyPlayed();
    const filtered = recent.filter(t => t.id !== track.id);
    const updated = [track, ...filtered].slice(0, 50); // Keep last 50
    return storage.set('recently_played', updated);
  },

  // Liked songs
  getLikedSongs: () => {
    return storage.get('liked_songs', []);
  },

  addLikedSong: (trackId) => {
    const liked = storage.getLikedSongs();
    if (!liked.includes(trackId)) {
      liked.push(trackId);
      return storage.set('liked_songs', liked);
    }
    return true;
  },

  removeLikedSong: (trackId) => {
    const liked = storage.getLikedSongs();
    const updated = liked.filter(id => id !== trackId);
    return storage.set('liked_songs', updated);
  },

  isLikedSong: (trackId) => {
    const liked = storage.getLikedSongs();
    return liked.includes(trackId);
  },

  // Playlists
  getUserPlaylists: () => {
    return storage.get('user_playlists', []);
  },

  createPlaylist: (playlist) => {
    const playlists = storage.getUserPlaylists();
    const newPlaylist = {
      ...playlist,
      id: Date.now(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    playlists.push(newPlaylist);
    storage.set('user_playlists', playlists);
    return newPlaylist;
  },

  updatePlaylist: (playlistId, updates) => {
    const playlists = storage.getUserPlaylists();
    const index = playlists.findIndex(p => p.id === playlistId);
    if (index !== -1) {
      playlists[index] = {
        ...playlists[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      storage.set('user_playlists', playlists);
      return playlists[index];
    }
    return null;
  },

  deletePlaylist: (playlistId) => {
    const playlists = storage.getUserPlaylists();
    const updated = playlists.filter(p => p.id !== playlistId);
    return storage.set('user_playlists', updated);
  },

  // Search history
  getSearchHistory: () => {
    return storage.get('search_history', []);
  },

  addToSearchHistory: (query) => {
    const history = storage.getSearchHistory();
    const filtered = history.filter(q => q.toLowerCase() !== query.toLowerCase());
    const updated = [query, ...filtered].slice(0, 20); // Keep last 20
    return storage.set('search_history', updated);
  },

  clearSearchHistory: () => {
    return storage.set('search_history', []);
  },

  // Player state
  getPlayerState: () => {
    return storage.get('player_state', {
      currentTrack: null,
      isPlaying: false,
      volume: 0.7,
      currentTime: 0,
      queue: [],
      currentIndex: 0,
      shuffle: false,
      repeat: 'none' // 'none', 'one', 'all'
    });
  },

  setPlayerState: (state) => {
    const current = storage.getPlayerState();
    const updated = { ...current, ...state };
    return storage.set('player_state', updated);
  },

  // Downloaded music (offline)
  getDownloadedMusic: () => {
    return storage.get('downloaded_music', []);
  },

  addDownloadedTrack: (trackId) => {
    const downloaded = storage.getDownloadedMusic();
    if (!downloaded.includes(trackId)) {
      downloaded.push(trackId);
      return storage.set('downloaded_music', downloaded);
    }
    return true;
  },

  removeDownloadedTrack: (trackId) => {
    const downloaded = storage.getDownloadedMusic();
    const updated = downloaded.filter(id => id !== trackId);
    return storage.set('downloaded_music', updated);
  },

  isDownloaded: (trackId) => {
    const downloaded = storage.getDownloadedMusic();
    return downloaded.includes(trackId);
  },

  // Focus mode sessions
  getFocusSessions: () => {
    return storage.get('focus_sessions', []);
  },

  addFocusSession: (session) => {
    const sessions = storage.getFocusSessions();
    const newSession = {
      ...session,
      id: Date.now(),
      timestamp: new Date().toISOString()
    };
    sessions.push(newSession);
    // Keep only last 100 sessions
    const updated = sessions.slice(-100);
    storage.set('focus_sessions', updated);
    return newSession;
  },

  // Social features
  getFollowedArtists: () => {
    return storage.get('followed_artists', []);
  },

  followArtist: (artistId) => {
    const followed = storage.getFollowedArtists();
    if (!followed.includes(artistId)) {
      followed.push(artistId);
      return storage.set('followed_artists', followed);
    }
    return true;
  },

  unfollowArtist: (artistId) => {
    const followed = storage.getFollowedArtists();
    const updated = followed.filter(id => id !== artistId);
    return storage.set('followed_artists', updated);
  },

  isFollowingArtist: (artistId) => {
    const followed = storage.getFollowedArtists();
    return followed.includes(artistId);
  },

  // Analytics and stats
  getListeningStats: () => {
    return storage.get('listening_stats', {
      totalListeningTime: 0,
      songsPlayed: 0,
      sessionsCount: 0,
      topGenres: {},
      topArtists: {},
      topTracks: {},
      dailyStats: {}
    });
  },

  updateListeningStats: (trackId, duration, artist, genre) => {
    const stats = storage.getListeningStats();
    const today = new Date().toDateString();
    
    // Update totals
    stats.totalListeningTime += duration;
    stats.songsPlayed += 1;
    
    // Update top genres
    stats.topGenres[genre] = (stats.topGenres[genre] || 0) + 1;
    
    // Update top artists
    stats.topArtists[artist] = (stats.topArtists[artist] || 0) + 1;
    
    // Update top tracks
    stats.topTracks[trackId] = (stats.topTracks[trackId] || 0) + 1;
    
    // Update daily stats
    if (!stats.dailyStats[today]) {
      stats.dailyStats[today] = { time: 0, songs: 0 };
    }
    stats.dailyStats[today].time += duration;
    stats.dailyStats[today].songs += 1;
    
    return storage.set('listening_stats', stats);
  },

  // Cache management
  getCacheInfo: () => {
    const keys = Object.keys(localStorage).filter(key => key.startsWith('spotify2_'));
    let totalSize = 0;
    
    keys.forEach(key => {
      totalSize += localStorage.getItem(key).length;
    });
    
    return {
      keys: keys.length,
      size: totalSize,
      sizeFormatted: `${(totalSize / 1024).toFixed(2)} KB`
    };
  }
};

// React hook for localStorage
export const useLocalStorage = (key, initialValue) => {
  const [storedValue, setStoredValue] = useState(() => {
    return storage.get(key, initialValue);
  });

  const setValue = (value) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      storage.set(key, valueToStore);
    } catch (error) {
      console.error('Error setting localStorage value:', error);
    }
  };

  return [storedValue, setValue];
};

