import React from 'react';
import { Clock, TrendingUp, Heart, Music } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { AlbumCard } from '@/components/UI/AlbumCard';
import { PlaylistCard } from '@/components/UI/PlaylistCard';
import { TrackCard } from '@/components/UI/TrackCard';

// Mock data
const recentlyPlayed = [
  { id: 1, title: 'Blinding Lights', artist: 'The Weeknd', album: 'After Hours', duration: 200, albumArt: null },
  { id: 2, title: 'Watermelon Sugar', artist: '<PERSON>', album: 'Fine Line', duration: 174, albumArt: null },
  { id: 3, title: 'Levitating', artist: '<PERSON><PERSON> Lipa', album: 'Future Nostalgia', duration: 203, albumArt: null },
  { id: 4, title: 'Good 4 U', artist: '<PERSON>', album: 'SOUR', duration: 178, albumArt: null },
];

const recommendedAlbums = [
  { id: 1, title: 'After Hours', artist: 'The Weeknd', year: 2020, trackCount: 14, coverArt: null },
  { id: 2, title: 'Future Nostalgia', artist: 'Dua Lipa', year: 2020, trackCount: 11, coverArt: null },
  { id: 3, title: 'Fine Line', artist: 'Harry Styles', year: 2019, trackCount: 12, coverArt: null },
  { id: 4, title: 'SOUR', artist: 'Olivia Rodrigo', year: 2021, trackCount: 11, coverArt: null },
  { id: 5, title: 'Positions', artist: 'Ariana Grande', year: 2020, trackCount: 14, coverArt: null },
];

const featuredPlaylists = [
  { 
    id: 1, 
    name: 'Today\'s Top Hits', 
    description: 'The most played songs right now', 
    trackCount: 50, 
    creator: 'Spotify',
    coverArt: null,
    isPublic: true
  },
  { 
    id: 2, 
    name: 'Chill Hits', 
    description: 'Kick back to the best new and recent chill hits', 
    trackCount: 75, 
    creator: 'Spotify',
    coverArt: null,
    isPublic: true
  },
  { 
    id: 3, 
    name: 'Workout Motivation', 
    description: 'High-energy tracks to power your workout', 
    trackCount: 100, 
    creator: 'Spotify',
    coverArt: null,
    isPublic: true
  },
  { 
    id: 4, 
    name: 'Focus Flow', 
    description: 'Instrumental beats to help you focus', 
    trackCount: 60, 
    creator: 'Spotify',
    coverArt: null,
    isPublic: true
  },
];

export function HomePage() {
  const currentHour = new Date().getHours();
  const getGreeting = () => {
    if (currentHour < 12) return 'Good morning';
    if (currentHour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const handlePlayTrack = (track) => {
    console.log('Playing track:', track);
  };

  const handlePlayAlbum = (album) => {
    console.log('Playing album:', album);
  };

  const handlePlayPlaylist = (playlist) => {
    console.log('Playing playlist:', playlist);
  };

  const handleNavigateToAlbum = (album) => {
    console.log('Navigate to album:', album);
  };

  const handleNavigateToPlaylist = (playlist) => {
    console.log('Navigate to playlist:', playlist);
  };

  return (
    <div className="p-6 space-y-8">
      {/* Greeting */}
      <div>
        <h1 className="text-3xl font-bold mb-2">{getGreeting()}</h1>
        <p className="text-muted-foreground">Welcome back to your music</p>
      </div>

      {/* Quick Access Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card className="group hover:bg-muted/50 transition-colors cursor-pointer">
          <CardContent className="p-4 flex items-center gap-4">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-400 rounded-lg flex items-center justify-center">
              <Heart className="h-8 w-8 text-white" />
            </div>
            <div>
              <h3 className="font-semibold">Liked Songs</h3>
              <p className="text-sm text-muted-foreground">127 songs</p>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:bg-muted/50 transition-colors cursor-pointer">
          <CardContent className="p-4 flex items-center gap-4">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-400 rounded-lg flex items-center justify-center">
              <Clock className="h-8 w-8 text-white" />
            </div>
            <div>
              <h3 className="font-semibold">Recently Played</h3>
              <p className="text-sm text-muted-foreground">Your recent tracks</p>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:bg-muted/50 transition-colors cursor-pointer">
          <CardContent className="p-4 flex items-center gap-4">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-8 w-8 text-white" />
            </div>
            <div>
              <h3 className="font-semibold">Discover Weekly</h3>
              <p className="text-sm text-muted-foreground">Your weekly mix</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recently Played */}
      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Recently played</h2>
          <Button variant="ghost" size="sm">Show all</Button>
        </div>
        <div className="space-y-2">
          {recentlyPlayed.slice(0, 4).map((track, index) => (
            <TrackCard
              key={track.id}
              track={track}
              index={index}
              onPlay={handlePlayTrack}
            />
          ))}
        </div>
      </section>

      {/* Made for You */}
      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Made for you</h2>
          <Button variant="ghost" size="sm">Show all</Button>
        </div>
        <ScrollArea className="w-full whitespace-nowrap">
          <div className="flex space-x-4 pb-4">
            {featuredPlaylists.map((playlist) => (
              <PlaylistCard
                key={playlist.id}
                playlist={playlist}
                onPlay={handlePlayPlaylist}
                onNavigate={handleNavigateToPlaylist}
                className="w-48 flex-shrink-0"
              />
            ))}
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </section>

      {/* Recently Released */}
      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Recently released</h2>
          <Button variant="ghost" size="sm">Show all</Button>
        </div>
        <ScrollArea className="w-full whitespace-nowrap">
          <div className="flex space-x-4 pb-4">
            {recommendedAlbums.map((album) => (
              <AlbumCard
                key={album.id}
                album={album}
                onPlay={handlePlayAlbum}
                onNavigate={handleNavigateToAlbum}
                className="w-48 flex-shrink-0"
              />
            ))}
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </section>

      {/* Popular Artists */}
      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Popular artists</h2>
          <Button variant="ghost" size="sm">Show all</Button>
        </div>
        <ScrollArea className="w-full whitespace-nowrap">
          <div className="flex space-x-4 pb-4">
            {['The Weeknd', 'Dua Lipa', 'Harry Styles', 'Olivia Rodrigo', 'Ariana Grande'].map((artist, index) => (
              <Card key={index} className="w-48 flex-shrink-0 group hover:bg-muted/50 transition-colors cursor-pointer">
                <CardContent className="p-4 text-center">
                  <div className="w-32 h-32 mx-auto bg-gradient-to-br from-orange-500 to-red-400 rounded-full mb-4 flex items-center justify-center">
                    <Music className="h-16 w-16 text-white/80" />
                  </div>
                  <h3 className="font-semibold group-hover:text-green-500 transition-colors">{artist}</h3>
                  <p className="text-sm text-muted-foreground">Artist</p>
                </CardContent>
              </Card>
            ))}
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </section>
    </div>
  );
}

