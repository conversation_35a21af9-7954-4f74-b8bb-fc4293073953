import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Play, Heart, MoreHorizontal, Download, Share2, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { usePlayer, usePlayerDispatch, playerActions } from '@/contexts/PlayerContext';
import { getAlbumById, getTracksByIds } from '@/utils/mockData';

export function AlbumView() {
  const { id } = useParams();
  const [album, setAlbum] = useState(null);
  const [tracks, setTracks] = useState([]);
  const [isLiked, setIsLiked] = useState(false);
  
  const { currentTrack, isPlaying } = usePlayer();
  const dispatch = usePlayerDispatch();

  useEffect(() => {
    if (id) {
      const albumData = getAlbumById(parseInt(id));
      if (albumData) {
        setAlbum(albumData);
        const albumTracks = getTracksByIds(albumData.tracks);
        setTracks(albumTracks);
      }
    }
  }, [id]);

  const handlePlayAlbum = () => {
    if (tracks.length > 0) {
      dispatch(playerActions.setQueue(tracks));
      dispatch(playerActions.setCurrentIndex(0));
      dispatch(playerActions.setCurrentTrack(tracks[0]));
      if (!isPlaying) {
        dispatch(playerActions.togglePlay());
      }
    }
  };

  const handlePlayTrack = (track, index) => {
    dispatch(playerActions.setQueue(tracks));
    dispatch(playerActions.setCurrentIndex(index));
    dispatch(playerActions.setCurrentTrack(track));
    if (!isPlaying || currentTrack?.id !== track.id) {
      dispatch(playerActions.togglePlay());
    }
  };

  const toggleLike = () => {
    setIsLiked(!isLiked);
  };

  const getTotalDuration = () => {
    return tracks.reduce((total, track) => total + track.duration, 0);
  };

  const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours} hr ${minutes} min`;
    }
    return `${minutes} min`;
  };

  if (!album) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Album not found</h2>
          <p className="text-muted-foreground">The album you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-full">
      {/* Header */}
      <div className="bg-gradient-to-b from-orange-600/20 to-background p-8">
        <div className="flex items-end gap-6">
          {/* Album Cover */}
          <div className="w-60 h-60 bg-gradient-to-br from-orange-500 to-red-400 rounded-lg shadow-2xl flex items-center justify-center">
            <span className="text-white text-6xl">♫</span>
          </div>

          {/* Album Info */}
          <div className="flex-1 min-w-0">
            <Badge variant="secondary" className="mb-2">Album</Badge>
            <h1 className="text-5xl font-bold mb-4 truncate">{album.title}</h1>
            
            <div className="flex items-center gap-2 text-lg mb-4">
              <span className="font-semibold">{album.artist}</span>
              <span className="text-muted-foreground">•</span>
              <span className="text-muted-foreground">{album.year}</span>
              <span className="text-muted-foreground">•</span>
              <span className="text-muted-foreground">{album.trackCount} songs</span>
              <span className="text-muted-foreground">•</span>
              <span className="text-muted-foreground">{formatDuration(getTotalDuration())}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="px-8 py-6 bg-gradient-to-b from-background/50 to-background">
        <div className="flex items-center gap-4">
          <Button
            onClick={handlePlayAlbum}
            size="lg"
            className="w-14 h-14 rounded-full bg-green-500 hover:bg-green-600 hover:scale-105 transition-all"
          >
            <Play className="h-6 w-6 ml-1" />
          </Button>

          <Button
            variant="ghost"
            size="lg"
            onClick={toggleLike}
            className="w-12 h-12 rounded-full hover:bg-muted"
          >
            <Heart className={`h-6 w-6 ${isLiked ? 'fill-green-500 text-green-500' : ''}`} />
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="lg" className="w-12 h-12 rounded-full hover:bg-muted">
                <MoreHorizontal className="h-6 w-6" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Download
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Heart className="h-4 w-4 mr-2" />
                Add to Liked Songs
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Track List */}
      <div className="px-8 pb-8">
        {/* Header Row */}
        <div className="grid grid-cols-[16px_1fr_60px] gap-4 px-4 py-2 text-sm text-muted-foreground border-b border-border mb-4">
          <div>#</div>
          <div>Title</div>
          <div className="flex justify-center">
            <Clock className="h-4 w-4" />
          </div>
        </div>

        {/* Tracks */}
        <div className="space-y-1">
          {tracks.map((track, index) => {
            const isCurrentTrack = currentTrack?.id === track.id;
            const isCurrentlyPlaying = isCurrentTrack && isPlaying;

            return (
              <div
                key={track.id}
                className="grid grid-cols-[16px_1fr_60px] gap-4 px-4 py-3 rounded-lg hover:bg-muted/50 group transition-colors cursor-pointer"
                onClick={() => handlePlayTrack(track, index)}
              >
                {/* Index/Play Button */}
                <div className="flex items-center justify-center">
                  {isCurrentlyPlaying ? (
                    <div className="w-4 h-4 flex items-center justify-center">
                      <div className="flex gap-0.5">
                        <div className="w-0.5 h-3 bg-green-500 animate-pulse" />
                        <div className="w-0.5 h-2 bg-green-500 animate-pulse" style={{ animationDelay: '0.1s' }} />
                        <div className="w-0.5 h-4 bg-green-500 animate-pulse" style={{ animationDelay: '0.2s' }} />
                      </div>
                    </div>
                  ) : (
                    <span className={`text-sm ${isCurrentTrack ? 'text-green-500' : 'group-hover:hidden'}`}>
                      {index + 1}
                    </span>
                  )}
                  {!isCurrentlyPlaying && (
                    <Play className="h-4 w-4 hidden group-hover:block" />
                  )}
                </div>

                {/* Track Info */}
                <div className="flex items-center gap-3 min-w-0">
                  <div className="min-w-0">
                    <p className={`font-medium truncate ${isCurrentTrack ? 'text-green-500' : ''}`}>
                      {track.title}
                    </p>
                    <p className="text-sm text-muted-foreground truncate">{track.artist}</p>
                  </div>
                </div>

                {/* Duration */}
                <div className="flex items-center justify-center">
                  <span className="text-sm text-muted-foreground">
                    {Math.floor(track.duration / 60)}:{(track.duration % 60).toString().padStart(2, '0')}
                  </span>
                </div>
              </div>
            );
          })}
        </div>

        {tracks.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-lg font-semibold mb-2">No tracks available</h3>
            <p className="text-muted-foreground">This album doesn't have any tracks yet.</p>
          </div>
        )}
      </div>

      {/* Album Info Section */}
      <div className="px-8 pb-8">
        <div className="border-t border-border pt-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">About this album</h3>
              <div className="space-y-2 text-sm text-muted-foreground">
                <p><span className="font-medium text-foreground">Release Date:</span> {new Date(album.year, 0, 1).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
                <p><span className="font-medium text-foreground">Total Tracks:</span> {album.trackCount}</p>
                <p><span className="font-medium text-foreground">Duration:</span> {formatDuration(getTotalDuration())}</p>
                <p><span className="font-medium text-foreground">Genre:</span> Pop</p>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl font-bold mb-4">Credits</h3>
              <div className="space-y-2 text-sm text-muted-foreground">
                <p><span className="font-medium text-foreground">Artist:</span> {album.artist}</p>
                <p><span className="font-medium text-foreground">Producer:</span> Various</p>
                <p><span className="font-medium text-foreground">Label:</span> Record Label</p>
                <p><span className="font-medium text-foreground">Copyright:</span> © {album.year} {album.artist}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

