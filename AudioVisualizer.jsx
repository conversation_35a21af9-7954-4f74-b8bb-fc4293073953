import React, { useEffect, useRef, useState } from 'react';
import { usePlayer } from '@/contexts/PlayerContext';

export function AudioVisualizer({ width = 300, height = 150, type = 'bars' }) {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const dataArrayRef = useRef(null);
  const sourceRef = useRef(null);
  
  const { audioRef, isPlaying, currentTrack } = usePlayer();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (!audioRef.current || !isPlaying || !currentTrack) {
      return;
    }

    const initializeAudioContext = async () => {
      try {
        if (!audioContextRef.current) {
          audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
          analyserRef.current = audioContextRef.current.createAnalyser();
          analyserRef.current.fftSize = 256;
          
          const bufferLength = analyserRef.current.frequencyBinCount;
          dataArrayRef.current = new Uint8Array(bufferLength);
        }

        if (!sourceRef.current && audioRef.current) {
          sourceRef.current = audioContextRef.current.createMediaElementSource(audioRef.current);
          sourceRef.current.connect(analyserRef.current);
          analyserRef.current.connect(audioContextRef.current.destination);
        }

        if (audioContextRef.current.state === 'suspended') {
          await audioContextRef.current.resume();
        }

        setIsInitialized(true);
      } catch (error) {
        console.error('Error initializing audio context:', error);
      }
    };

    initializeAudioContext();
  }, [audioRef, isPlaying, currentTrack]);

  useEffect(() => {
    if (!isInitialized || !isPlaying) {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      return;
    }

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    canvas.width = width;
    canvas.height = height;

    const draw = () => {
      if (!analyserRef.current || !dataArrayRef.current) return;

      analyserRef.current.getByteFrequencyData(dataArrayRef.current);

      // Clear canvas
      ctx.clearRect(0, 0, width, height);

      if (type === 'bars') {
        drawBars(ctx, dataArrayRef.current, width, height);
      } else if (type === 'wave') {
        drawWave(ctx, dataArrayRef.current, width, height);
      } else if (type === 'circular') {
        drawCircular(ctx, dataArrayRef.current, width, height);
      }

      animationRef.current = requestAnimationFrame(draw);
    };

    draw();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isInitialized, isPlaying, width, height, type]);

  const drawBars = (ctx, dataArray, canvasWidth, canvasHeight) => {
    const barWidth = (canvasWidth / dataArray.length) * 2.5;
    let barHeight;
    let x = 0;

    for (let i = 0; i < dataArray.length; i++) {
      barHeight = (dataArray[i] / 255) * canvasHeight;

      // Create gradient
      const gradient = ctx.createLinearGradient(0, canvasHeight - barHeight, 0, canvasHeight);
      gradient.addColorStop(0, '#1db954');
      gradient.addColorStop(0.5, '#1ed760');
      gradient.addColorStop(1, '#1db954');

      ctx.fillStyle = gradient;
      ctx.fillRect(x, canvasHeight - barHeight, barWidth, barHeight);

      x += barWidth + 1;
    }
  };

  const drawWave = (ctx, dataArray, canvasWidth, canvasHeight) => {
    ctx.lineWidth = 2;
    ctx.strokeStyle = '#1db954';
    ctx.beginPath();

    const sliceWidth = canvasWidth / dataArray.length;
    let x = 0;

    for (let i = 0; i < dataArray.length; i++) {
      const v = dataArray[i] / 128.0;
      const y = (v * canvasHeight) / 2;

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }

      x += sliceWidth;
    }

    ctx.lineTo(canvasWidth, canvasHeight / 2);
    ctx.stroke();
  };

  const drawCircular = (ctx, dataArray, canvasWidth, canvasHeight) => {
    const centerX = canvasWidth / 2;
    const centerY = canvasHeight / 2;
    const radius = Math.min(centerX, centerY) - 20;

    ctx.lineWidth = 2;
    ctx.strokeStyle = '#1db954';

    for (let i = 0; i < dataArray.length; i++) {
      const angle = (i / dataArray.length) * 2 * Math.PI;
      const amplitude = (dataArray[i] / 255) * radius * 0.5;
      
      const x1 = centerX + Math.cos(angle) * radius;
      const y1 = centerY + Math.sin(angle) * radius;
      const x2 = centerX + Math.cos(angle) * (radius + amplitude);
      const y2 = centerY + Math.sin(angle) * (radius + amplitude);

      ctx.beginPath();
      ctx.moveTo(x1, y1);
      ctx.lineTo(x2, y2);
      ctx.stroke();
    }
  };

  // Fallback visualization when audio context is not available
  const drawFallback = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    canvas.width = width;
    canvas.height = height;

    // Simple animated bars as fallback
    const bars = 32;
    const barWidth = width / bars;
    
    for (let i = 0; i < bars; i++) {
      const barHeight = Math.random() * height * (isPlaying ? 0.8 : 0.2);
      const gradient = ctx.createLinearGradient(0, height - barHeight, 0, height);
      gradient.addColorStop(0, '#1db954');
      gradient.addColorStop(1, '#1ed760');
      
      ctx.fillStyle = gradient;
      ctx.fillRect(i * barWidth, height - barHeight, barWidth - 1, barHeight);
    }
  };

  useEffect(() => {
    if (!isInitialized && isPlaying) {
      const fallbackInterval = setInterval(drawFallback, 100);
      return () => clearInterval(fallbackInterval);
    }
  }, [isInitialized, isPlaying, width, height]);

  return (
    <div className="flex items-center justify-center">
      <canvas
        ref={canvasRef}
        className="rounded-lg bg-black/10 dark:bg-white/5"
        style={{ width: `${width}px`, height: `${height}px` }}
      />
    </div>
  );
}

