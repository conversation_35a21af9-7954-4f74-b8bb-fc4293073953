<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Spotify 2.0 - Music Streaming App</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#22c55e" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Spotify 2.0" />
    <meta name="mobile-web-app-capable" content="yes" />
    
    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png" />
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Spotify 2.0 - A modern music streaming application with enhanced features including mood-based playlists, focus mode, social sharing, and audio visualization." />
    <meta name="keywords" content="music, streaming, spotify, playlists, audio, songs, artists, albums" />
    <meta name="author" content="Spotify 2.0 Team" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Spotify 2.0 - Music Streaming App" />
    <meta property="og:description" content="A modern music streaming application with enhanced features" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://spotify2.app" />
    <meta property="og:image" content="/icons/icon-512x512.png" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Spotify 2.0 - Music Streaming App" />
    <meta name="twitter:description" content="A modern music streaming application with enhanced features" />
    <meta name="twitter:image" content="/icons/icon-512x512.png" />
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="/fonts/inter.woff2" as="font" type="font/woff2" crossorigin />
    
    <!-- DNS Prefetch for External Resources -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//fonts.gstatic.com" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    
    <!-- Service Worker Registration -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>l>
