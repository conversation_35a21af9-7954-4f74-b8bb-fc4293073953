import React, { useEffect, useRef } from 'react';
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward, 
  Volume2, 
  VolumeX,
  Shuffle,
  Repeat,
  Repeat1,
  Heart,
  Maximize2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { usePlayer, usePlayerDispatch, playerActions } from '@/contexts/PlayerContext';

export function MiniPlayer({ onExpand }) {
  const player = usePlayer();
  const dispatch = usePlayerDispatch();
  const progressIntervalRef = useRef(null);

  const {
    currentTrack,
    isPlaying,
    volume,
    isMuted,
    currentTime,
    duration,
    repeatMode,
    isShuffled,
    audioRef
  } = player;

  // Update progress
  useEffect(() => {
    if (isPlaying && audioRef.current) {
      progressIntervalRef.current = setInterval(() => {
        if (audioRef.current) {
          dispatch(playerActions.setCurrentTime(audioRef.current.currentTime));
        }
      }, 1000);
    } else {
      clearInterval(progressIntervalRef.current);
    }

    return () => clearInterval(progressIntervalRef.current);
  }, [isPlaying, dispatch]);

  // Handle audio events
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      dispatch(playerActions.setDuration(audio.duration));
    };

    const handleTimeUpdate = () => {
      dispatch(playerActions.setCurrentTime(audio.currentTime));
    };

    const handleEnded = () => {
      if (repeatMode === 'one') {
        audio.currentTime = 0;
        audio.play();
      } else {
        dispatch(playerActions.nextTrack());
      }
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [dispatch, repeatMode]);

  // Control audio playback
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.play().catch(console.error);
    } else {
      audio.pause();
    }
  }, [isPlaying]);

  // Control volume
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.volume = isMuted ? 0 : volume;
  }, [volume, isMuted]);

  // Load new track
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio || !currentTrack) return;

    audio.src = currentTrack.audioUrl;
    audio.load();
  }, [currentTrack]);

  const handlePlayPause = () => {
    dispatch(playerActions.togglePlay());
  };

  const handlePrevious = () => {
    dispatch(playerActions.previousTrack());
  };

  const handleNext = () => {
    dispatch(playerActions.nextTrack());
  };

  const handleProgressChange = (value) => {
    const newTime = (value[0] / 100) * duration;
    if (audioRef.current) {
      audioRef.current.currentTime = newTime;
    }
    dispatch(playerActions.setCurrentTime(newTime));
  };

  const handleVolumeChange = (value) => {
    dispatch(playerActions.setVolume(value[0] / 100));
  };

  const handleMute = () => {
    dispatch(playerActions.toggleMute());
  };

  const handleShuffle = () => {
    dispatch(playerActions.toggleShuffle());
  };

  const handleRepeat = () => {
    dispatch(playerActions.setRepeatMode());
  };

  const formatTime = (time) => {
    if (isNaN(time)) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getRepeatIcon = () => {
    switch (repeatMode) {
      case 'one':
        return <Repeat1 className="h-4 w-4" />;
      case 'all':
        return <Repeat className="h-4 w-4" />;
      default:
        return <Repeat className="h-4 w-4" />;
    }
  };

  if (!currentTrack) {
    return null;
  }

  return (
    <div className="bg-card border-t border-border p-4">
      {/* Hidden audio element */}
      <audio ref={audioRef} preload="metadata" />
      
      <div className="flex items-center justify-between">
        {/* Track Info */}
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className="w-12 h-12 bg-muted rounded-md flex-shrink-0 overflow-hidden">
            {currentTrack.albumArt ? (
              <img 
                src={currentTrack.albumArt} 
                alt={currentTrack.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-green-500 to-emerald-400" />
            )}
          </div>
          <div className="min-w-0 flex-1">
            <div className="font-medium text-sm truncate">{currentTrack.title}</div>
            <div className="text-xs text-muted-foreground truncate">{currentTrack.artist}</div>
          </div>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Heart className="h-4 w-4" />
          </Button>
        </div>

        {/* Player Controls */}
        <div className="flex flex-col items-center gap-2 flex-1 max-w-md">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleShuffle}
              className={`h-8 w-8 p-0 ${isShuffled ? 'text-green-500' : ''}`}
            >
              <Shuffle className="h-4 w-4" />
            </Button>
            
            <Button variant="ghost" size="sm" onClick={handlePrevious} className="h-8 w-8 p-0">
              <SkipBack className="h-4 w-4" />
            </Button>
            
            <Button 
              variant="default" 
              size="sm" 
              onClick={handlePlayPause}
              className="h-10 w-10 rounded-full bg-green-500 hover:bg-green-600 text-white"
            >
              {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5 ml-0.5" />}
            </Button>
            
            <Button variant="ghost" size="sm" onClick={handleNext} className="h-8 w-8 p-0">
              <SkipForward className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRepeat}
              className={`h-8 w-8 p-0 ${repeatMode !== 'off' ? 'text-green-500' : ''}`}
            >
              {getRepeatIcon()}
            </Button>
          </div>

          {/* Progress Bar */}
          <div className="flex items-center gap-2 w-full">
            <span className="text-xs text-muted-foreground w-10 text-right">
              {formatTime(currentTime)}
            </span>
            <Slider
              value={[duration > 0 ? (currentTime / duration) * 100 : 0]}
              onValueChange={handleProgressChange}
              max={100}
              step={1}
              className="flex-1"
            />
            <span className="text-xs text-muted-foreground w-10">
              {formatTime(duration)}
            </span>
          </div>
        </div>

        {/* Volume and Expand */}
        <div className="flex items-center gap-2 flex-1 justify-end">
          <Button variant="ghost" size="sm" onClick={handleMute} className="h-8 w-8 p-0">
            {isMuted || volume === 0 ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
          </Button>
          
          <div className="w-20">
            <Slider
              value={[isMuted ? 0 : volume * 100]}
              onValueChange={handleVolumeChange}
              max={100}
              step={1}
            />
          </div>

          {onExpand && (
            <Button variant="ghost" size="sm" onClick={onExpand} className="h-8 w-8 p-0">
              <Maximize2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

