import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  Search, 
  Library, 
  Plus, 
  Heart,
  Music,
  Radio,
  Mic2,
  ChevronLeft,
  ChevronRight,
  Download
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';

export function Sidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const location = useLocation();

  const mainNavItems = [
    { icon: Home, label: 'Home', path: '/' },
    { icon: Search, label: 'Search', path: '/search' },
    { icon: Library, label: 'Your Library', path: '/library' },
  ];

  const libraryItems = [
    { icon: Plus, label: 'Create Playlist', path: '/create-playlist' },
    { icon: Heart, label: 'Liked Songs', path: '/liked' },
    { icon: Download, label: 'Downloaded', path: '/downloaded' },
  ];

  const playlists = [
    'My Playlist #1',
    'Chill Vibes',
    'Workout Mix',
    'Road Trip Songs',
    'Focus Mode',
    'Party Hits',
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <div className={`bg-sidebar border-r border-sidebar-border transition-all duration-300 ${
      isCollapsed ? 'w-16' : 'w-64'
    } flex flex-col h-full`}>
      {/* Header with collapse toggle */}
      <div className="p-4 flex items-center justify-between">
        {!isCollapsed && (
          <div className="flex items-center gap-2">
            <Music className="h-8 w-8 text-green-500" />
            <span className="text-xl font-bold bg-gradient-to-r from-green-500 to-emerald-400 bg-clip-text text-transparent">
              Spotify 2.0
            </span>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="h-8 w-8 p-0"
        >
          {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </Button>
      </div>

      <div className="flex-1 flex flex-col">
        {/* Main Navigation */}
        <nav className="px-2">
          {mainNavItems.map((item) => {
            const Icon = item.icon;
            return (
              <Link key={item.path} to={item.path}>
                <Button
                  variant={isActive(item.path) ? "secondary" : "ghost"}
                  className={`w-full justify-start mb-1 ${
                    isCollapsed ? 'px-2' : 'px-3'
                  } ${isActive(item.path) ? 'bg-sidebar-accent text-sidebar-accent-foreground' : ''}`}
                >
                  <Icon className="h-5 w-5" />
                  {!isCollapsed && <span className="ml-3">{item.label}</span>}
                </Button>
              </Link>
            );
          })}
        </nav>

        <Separator className="my-4 mx-4" />

        {/* Library Section */}
        <div className="px-2">
          {libraryItems.map((item) => {
            const Icon = item.icon;
            return (
              <Link key={item.path} to={item.path}>
                <Button
                  variant={isActive(item.path) ? "secondary" : "ghost"}
                  className={`w-full justify-start mb-1 ${
                    isCollapsed ? 'px-2' : 'px-3'
                  } ${isActive(item.path) ? 'bg-sidebar-accent text-sidebar-accent-foreground' : ''}`}
                >
                  <Icon className="h-5 w-5" />
                  {!isCollapsed && <span className="ml-3">{item.label}</span>}
                </Button>
              </Link>
            );
          })}
        </div>

        {!isCollapsed && (
          <>
            <Separator className="my-4 mx-4" />

            {/* Playlists */}
            <div className="flex-1 px-2">
              <div className="text-sm font-medium text-sidebar-foreground/70 px-3 mb-2">
                Playlists
              </div>
              <ScrollArea className="h-full">
                {playlists.map((playlist, index) => (
                  <Link key={index} to={`/playlist/${index}`}>
                    <Button
                      variant="ghost"
                      className="w-full justify-start mb-1 px-3 text-sm font-normal text-sidebar-foreground/80 hover:text-sidebar-foreground"
                    >
                      <Music className="h-4 w-4 mr-3" />
                      {playlist}
                    </Button>
                  </Link>
                ))}
              </ScrollArea>
            </div>
          </>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-sidebar-border">
        {!isCollapsed && (
          <div className="text-xs text-sidebar-foreground/60">
            <p>Made with ❤️ by Manus AI</p>
          </div>
        )}
      </div>
    </div>
  );
}

