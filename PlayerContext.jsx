import React, { createContext, useContext, useReducer, useRef } from 'react';

// Initial state for the music player
const initialState = {
  currentTrack: null,
  isPlaying: false,
  volume: 0.7,
  isMuted: false,
  currentTime: 0,
  duration: 0,
  queue: [],
  currentIndex: 0,
  isShuffled: false,
  repeatMode: 'off', // 'off', 'one', 'all'
  isLoading: false,
  crossfadeEnabled: false,
};

// Action types
const ACTIONS = {
  SET_CURRENT_TRACK: 'SET_CURRENT_TRACK',
  TOGGLE_PLAY: 'TOGGLE_PLAY',
  SET_VOLUME: 'SET_VOLUME',
  TOGGLE_MUTE: 'TOGGLE_MUTE',
  SET_CURRENT_TIME: 'SET_CURRENT_TIME',
  SET_DURATION: 'SET_DURATION',
  SET_QUEUE: 'SET_QUEUE',
  SET_CURRENT_INDEX: 'SET_CURRENT_INDEX',
  TOGGLE_SHUFFLE: 'TOGGLE_SHUFFLE',
  SET_REPEAT_MODE: 'SET_REPEAT_MODE',
  SET_LOADING: 'SET_LOADING',
  TOGGLE_CROSSFADE: 'TOGGLE_CROSSFADE',
  NEXT_TRACK: 'NEXT_TRACK',
  PREVIOUS_TRACK: 'PREVIOUS_TRACK',
  ADD_TO_QUEUE: 'ADD_TO_QUEUE',
  REMOVE_FROM_QUEUE: 'REMOVE_FROM_QUEUE',
  REORDER_QUEUE: 'REORDER_QUEUE',
};

// Reducer function
function playerReducer(state, action) {
  switch (action.type) {
    case ACTIONS.SET_CURRENT_TRACK:
      return { ...state, currentTrack: action.payload };
    
    case ACTIONS.TOGGLE_PLAY:
      return { ...state, isPlaying: !state.isPlaying };
    
    case ACTIONS.SET_VOLUME:
      return { ...state, volume: action.payload, isMuted: false };
    
    case ACTIONS.TOGGLE_MUTE:
      return { ...state, isMuted: !state.isMuted };
    
    case ACTIONS.SET_CURRENT_TIME:
      return { ...state, currentTime: action.payload };
    
    case ACTIONS.SET_DURATION:
      return { ...state, duration: action.payload };
    
    case ACTIONS.SET_QUEUE:
      return { ...state, queue: action.payload };
    
    case ACTIONS.SET_CURRENT_INDEX:
      return { ...state, currentIndex: action.payload };
    
    case ACTIONS.TOGGLE_SHUFFLE:
      return { ...state, isShuffled: !state.isShuffled };
    
    case ACTIONS.SET_REPEAT_MODE:
      const modes = ['off', 'one', 'all'];
      const currentModeIndex = modes.indexOf(state.repeatMode);
      const nextMode = modes[(currentModeIndex + 1) % modes.length];
      return { ...state, repeatMode: nextMode };
    
    case ACTIONS.SET_LOADING:
      return { ...state, isLoading: action.payload };
    
    case ACTIONS.TOGGLE_CROSSFADE:
      return { ...state, crossfadeEnabled: !state.crossfadeEnabled };
    
    case ACTIONS.NEXT_TRACK:
      if (state.queue.length === 0) return state;
      let nextIndex = state.currentIndex + 1;
      if (nextIndex >= state.queue.length) {
        nextIndex = state.repeatMode === 'all' ? 0 : state.currentIndex;
      }
      return {
        ...state,
        currentIndex: nextIndex,
        currentTrack: state.queue[nextIndex] || null,
      };
    
    case ACTIONS.PREVIOUS_TRACK:
      if (state.queue.length === 0) return state;
      let prevIndex = state.currentIndex - 1;
      if (prevIndex < 0) {
        prevIndex = state.repeatMode === 'all' ? state.queue.length - 1 : 0;
      }
      return {
        ...state,
        currentIndex: prevIndex,
        currentTrack: state.queue[prevIndex] || null,
      };
    
    case ACTIONS.ADD_TO_QUEUE:
      return {
        ...state,
        queue: [...state.queue, action.payload],
      };
    
    case ACTIONS.REMOVE_FROM_QUEUE:
      const newQueue = state.queue.filter((_, index) => index !== action.payload);
      return {
        ...state,
        queue: newQueue,
        currentIndex: action.payload < state.currentIndex ? state.currentIndex - 1 : state.currentIndex,
      };
    
    case ACTIONS.REORDER_QUEUE:
      return {
        ...state,
        queue: action.payload,
      };
    
    default:
      return state;
  }
}

// Create contexts
const PlayerContext = createContext();
const PlayerDispatchContext = createContext();

// Provider component
export function PlayerProvider({ children }) {
  const [state, dispatch] = useReducer(playerReducer, initialState);
  const audioRef = useRef(null);

  return (
    <PlayerContext.Provider value={{ ...state, audioRef }}>
      <PlayerDispatchContext.Provider value={dispatch}>
        {children}
      </PlayerDispatchContext.Provider>
    </PlayerContext.Provider>
  );
}

// Custom hooks
export function usePlayer() {
  const context = useContext(PlayerContext);
  if (!context) {
    throw new Error('usePlayer must be used within a PlayerProvider');
  }
  return context;
}

export function usePlayerDispatch() {
  const context = useContext(PlayerDispatchContext);
  if (!context) {
    throw new Error('usePlayerDispatch must be used within a PlayerProvider');
  }
  return context;
}

// Action creators
export const playerActions = {
  setCurrentTrack: (track) => ({ type: ACTIONS.SET_CURRENT_TRACK, payload: track }),
  togglePlay: () => ({ type: ACTIONS.TOGGLE_PLAY }),
  setVolume: (volume) => ({ type: ACTIONS.SET_VOLUME, payload: volume }),
  toggleMute: () => ({ type: ACTIONS.TOGGLE_MUTE }),
  setCurrentTime: (time) => ({ type: ACTIONS.SET_CURRENT_TIME, payload: time }),
  setDuration: (duration) => ({ type: ACTIONS.SET_DURATION, payload: duration }),
  setQueue: (queue) => ({ type: ACTIONS.SET_QUEUE, payload: queue }),
  setCurrentIndex: (index) => ({ type: ACTIONS.SET_CURRENT_INDEX, payload: index }),
  toggleShuffle: () => ({ type: ACTIONS.TOGGLE_SHUFFLE }),
  setRepeatMode: () => ({ type: ACTIONS.SET_REPEAT_MODE }),
  setLoading: (loading) => ({ type: ACTIONS.SET_LOADING, payload: loading }),
  toggleCrossfade: () => ({ type: ACTIONS.TOGGLE_CROSSFADE }),
  nextTrack: () => ({ type: ACTIONS.NEXT_TRACK }),
  previousTrack: () => ({ type: ACTIONS.PREVIOUS_TRACK }),
  addToQueue: (track) => ({ type: ACTIONS.ADD_TO_QUEUE, payload: track }),
  removeFromQueue: (index) => ({ type: ACTIONS.REMOVE_FROM_QUEUE, payload: index }),
  reorderQueue: (newQueue) => ({ type: ACTIONS.REORDER_QUEUE, payload: newQueue }),
};

