import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Play, Pause, Heart, MoreHorizontal, Download, Share2, Clock, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { TrackCard } from '@/components/UI/TrackCard';
import { usePlayer, usePlayerDispatch, playerActions } from '@/contexts/PlayerContext';
import { getPlaylistById, getTracksByIds } from '@/utils/mockData';

export function PlaylistView() {
  const { id } = useParams();
  const [playlist, setPlaylist] = useState(null);
  const [tracks, setTracks] = useState([]);
  const [isLiked, setIsLiked] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  
  const { currentTrack, isPlaying } = usePlayer();
  const dispatch = usePlayerDispatch();

  useEffect(() => {
    if (id) {
      const playlistData = getPlaylistById(parseInt(id));
      if (playlistData) {
        setPlaylist(playlistData);
        const playlistTracks = getTracksByIds(playlistData.tracks);
        setTracks(playlistTracks);
        setIsFollowing(!playlistData.isOwner);
      }
    }
  }, [id]);

  const handlePlayPlaylist = () => {
    if (tracks.length > 0) {
      dispatch(playerActions.setQueue(tracks));
      dispatch(playerActions.setCurrentIndex(0));
      dispatch(playerActions.setCurrentTrack(tracks[0]));
      if (!isPlaying) {
        dispatch(playerActions.togglePlay());
      }
    }
  };

  const handlePlayTrack = (track, index) => {
    dispatch(playerActions.setQueue(tracks));
    dispatch(playerActions.setCurrentIndex(index));
    dispatch(playerActions.setCurrentTrack(track));
    if (!isPlaying || currentTrack?.id !== track.id) {
      dispatch(playerActions.togglePlay());
    }
  };

  const toggleLike = () => {
    setIsLiked(!isLiked);
  };

  const toggleFollow = () => {
    setIsFollowing(!isFollowing);
  };

  const getTotalDuration = () => {
    return tracks.reduce((total, track) => total + track.duration, 0);
  };

  const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours} hr ${minutes} min`;
    }
    return `${minutes} min`;
  };

  if (!playlist) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Playlist not found</h2>
          <p className="text-muted-foreground">The playlist you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-full">
      {/* Header */}
      <div className="bg-gradient-to-b from-purple-600/20 to-background p-8">
        <div className="flex items-end gap-6">
          {/* Playlist Cover */}
          <div className="w-60 h-60 bg-gradient-to-br from-purple-500 to-pink-400 rounded-lg shadow-2xl flex items-center justify-center">
            <span className="text-white text-6xl">♪</span>
          </div>

          {/* Playlist Info */}
          <div className="flex-1 min-w-0">
            <Badge variant="secondary" className="mb-2">
              {playlist.isPublic ? 'Public Playlist' : 'Private Playlist'}
            </Badge>
            <h1 className="text-5xl font-bold mb-4 truncate">{playlist.name}</h1>
            {playlist.description && (
              <p className="text-lg text-muted-foreground mb-4">{playlist.description}</p>
            )}
            
            <div className="flex items-center gap-2 text-sm">
              <Avatar className="w-6 h-6">
                <AvatarFallback className="text-xs">
                  {playlist.creator.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <span className="font-semibold">{playlist.creator}</span>
              <span className="text-muted-foreground">•</span>
              <span className="text-muted-foreground">{playlist.trackCount} songs</span>
              <span className="text-muted-foreground">•</span>
              <span className="text-muted-foreground">{formatDuration(getTotalDuration())}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="px-8 py-6 bg-gradient-to-b from-background/50 to-background">
        <div className="flex items-center gap-4">
          <Button
            onClick={handlePlayPlaylist}
            size="lg"
            className="w-14 h-14 rounded-full bg-green-500 hover:bg-green-600 hover:scale-105 transition-all"
          >
            <Play className="h-6 w-6 ml-1" />
          </Button>

          <Button
            variant="ghost"
            size="lg"
            onClick={toggleLike}
            className="w-12 h-12 rounded-full hover:bg-muted"
          >
            <Heart className={`h-6 w-6 ${isLiked ? 'fill-green-500 text-green-500' : ''}`} />
          </Button>

          {!playlist.isOwner && (
            <Button
              variant="outline"
              onClick={toggleFollow}
              className={isFollowing ? 'bg-green-500/10 border-green-500 text-green-500' : ''}
            >
              {isFollowing ? 'Following' : 'Follow'}
            </Button>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="lg" className="w-12 h-12 rounded-full hover:bg-muted">
                <MoreHorizontal className="h-6 w-6" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Download
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Heart className="h-4 w-4 mr-2" />
                Add to Liked Songs
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Track List */}
      <div className="px-8 pb-8">
        {/* Header Row */}
        <div className="grid grid-cols-[16px_1fr_1fr_1fr_60px] gap-4 px-4 py-2 text-sm text-muted-foreground border-b border-border mb-4">
          <div>#</div>
          <div>Title</div>
          <div>Album</div>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Date added
          </div>
          <div className="flex justify-center">
            <Clock className="h-4 w-4" />
          </div>
        </div>

        {/* Tracks */}
        <div className="space-y-1">
          {tracks.map((track, index) => {
            const isCurrentTrack = currentTrack?.id === track.id;
            const isCurrentlyPlaying = isCurrentTrack && isPlaying;

            return (
              <div
                key={track.id}
                className="grid grid-cols-[16px_1fr_1fr_1fr_60px] gap-4 px-4 py-3 rounded-lg hover:bg-muted/50 group transition-colors cursor-pointer"
                onClick={() => handlePlayTrack(track, index)}
              >
                {/* Index/Play Button */}
                <div className="flex items-center justify-center">
                  {isCurrentlyPlaying ? (
                    <div className="w-4 h-4 flex items-center justify-center">
                      <div className="flex gap-0.5">
                        <div className="w-0.5 h-3 bg-green-500 animate-pulse" />
                        <div className="w-0.5 h-2 bg-green-500 animate-pulse" style={{ animationDelay: '0.1s' }} />
                        <div className="w-0.5 h-4 bg-green-500 animate-pulse" style={{ animationDelay: '0.2s' }} />
                      </div>
                    </div>
                  ) : (
                    <span className={`text-sm ${isCurrentTrack ? 'text-green-500' : 'group-hover:hidden'}`}>
                      {index + 1}
                    </span>
                  )}
                  {!isCurrentlyPlaying && (
                    <Play className="h-4 w-4 hidden group-hover:block" />
                  )}
                </div>

                {/* Track Info */}
                <div className="flex items-center gap-3 min-w-0">
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-400 rounded-md flex-shrink-0" />
                  <div className="min-w-0">
                    <p className={`font-medium truncate ${isCurrentTrack ? 'text-green-500' : ''}`}>
                      {track.title}
                    </p>
                    <p className="text-sm text-muted-foreground truncate">{track.artist}</p>
                  </div>
                </div>

                {/* Album */}
                <div className="flex items-center min-w-0">
                  <p className="text-sm text-muted-foreground truncate">{track.album}</p>
                </div>

                {/* Date Added */}
                <div className="flex items-center min-w-0">
                  <p className="text-sm text-muted-foreground">
                    {new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}
                  </p>
                </div>

                {/* Duration */}
                <div className="flex items-center justify-center">
                  <span className="text-sm text-muted-foreground">
                    {Math.floor(track.duration / 60)}:{(track.duration % 60).toString().padStart(2, '0')}
                  </span>
                </div>
              </div>
            );
          })}
        </div>

        {tracks.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-lg font-semibold mb-2">This playlist is empty</h3>
            <p className="text-muted-foreground">Add some songs to get started!</p>
          </div>
        )}
      </div>
    </div>
  );
}

