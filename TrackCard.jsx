import React from 'react';
import { Play, Pause, Heart, MoreHorizontal, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { usePlayer, usePlayerDispatch, playerActions } from '@/contexts/PlayerContext';

export function TrackCard({ track, index, showIndex = false, onPlay, className }) {
  const player = usePlayer();
  const dispatch = usePlayerDispatch();
  
  const { currentTrack, isPlaying } = player;
  const isCurrentTrack = currentTrack?.id === track.id;
  const isTrackPlaying = isCurrentTrack && isPlaying;

  const handlePlayPause = () => {
    if (isCurrentTrack) {
      dispatch(playerActions.togglePlay());
    } else {
      dispatch(playerActions.setCurrentTrack(track));
      dispatch(playerActions.togglePlay());
    }
    
    if (onPlay) {
      onPlay(track);
    }
  };

  const formatDuration = (seconds) => {
    if (!seconds) return '0:00';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <Card className={`group hover:bg-muted/50 transition-colors ${className}`}>
      <CardContent className="p-3">
        <div className="flex items-center gap-3">
          {/* Index or Play Button */}
          <div className="w-8 h-8 flex items-center justify-center">
            {showIndex && !isCurrentTrack && (
              <span className="text-sm text-muted-foreground group-hover:hidden">
                {index + 1}
              </span>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePlayPause}
              className={`h-8 w-8 p-0 ${
                showIndex && !isCurrentTrack ? 'opacity-0 group-hover:opacity-100' : ''
              } ${isCurrentTrack ? 'text-green-500' : ''}`}
            >
              {isTrackPlaying ? (
                <Pause className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* Album Art */}
          <div className="w-12 h-12 bg-muted rounded-md flex-shrink-0 overflow-hidden">
            {track.albumArt ? (
              <img 
                src={track.albumArt} 
                alt={track.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-green-500 to-emerald-400" />
            )}
          </div>

          {/* Track Info */}
          <div className="flex-1 min-w-0">
            <div className={`font-medium text-sm truncate ${
              isCurrentTrack ? 'text-green-500' : ''
            }`}>
              {track.title}
            </div>
            <div className="text-xs text-muted-foreground truncate">
              {track.artist}
            </div>
          </div>

          {/* Album Name (hidden on mobile) */}
          <div className="hidden md:block flex-1 min-w-0">
            <div className="text-sm text-muted-foreground truncate">
              {track.album}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            {/* Like Button */}
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <Heart className="h-4 w-4" />
            </Button>

            {/* Duration */}
            <div className="text-sm text-muted-foreground w-12 text-right">
              {formatDuration(track.duration)}
            </div>

            {/* More Options */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Add to queue</DropdownMenuItem>
                <DropdownMenuItem>Add to playlist</DropdownMenuItem>
                <DropdownMenuItem>Go to artist</DropdownMenuItem>
                <DropdownMenuItem>Go to album</DropdownMenuItem>
                <DropdownMenuItem>Share</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

