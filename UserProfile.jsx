import React, { useState } from 'react';
import { <PERSON>r, <PERSON><PERSON><PERSON>, Crown, Music, Heart, Users, Calendar, Edit3, Camera } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';

const mockUserData = {
  id: 1,
  name: '<PERSON>',
  username: '@johndoe_music',
  email: '<EMAIL>',
  bio: 'Music lover and playlist curator. Always discovering new sounds and sharing great music with friends.',
  avatar: null,
  isPremium: true,
  joinDate: '2020-03-15',
  location: 'New York, NY',
  stats: {
    totalListeningTime: 2847, // hours
    songsPlayed: 15420,
    playlistsCreated: 23,
    followers: 156,
    following: 89,
    topGenre: 'Pop'
  },
  recentActivity: [
    { type: 'playlist', action: 'created', item: 'Summer Vibes 2024', time: '2 hours ago' },
    { type: 'song', action: 'liked', item: 'Blinding Lights - The Weeknd', time: '5 hours ago' },
    { type: 'artist', action: 'followed', item: 'Dua Lipa', time: '1 day ago' },
    { type: 'playlist', action: 'shared', item: 'Workout Mix', time: '2 days ago' }
  ],
  topArtists: [
    { name: 'The Weeknd', plays: 342 },
    { name: 'Dua Lipa', plays: 298 },
    { name: 'Harry Styles', plays: 256 },
    { name: 'Taylor Swift', plays: 234 },
    { name: 'Billie Eilish', plays: 198 }
  ],
  topTracks: [
    { title: 'Blinding Lights', artist: 'The Weeknd', plays: 89 },
    { title: 'Levitating', artist: 'Dua Lipa', plays: 76 },
    { title: 'Watermelon Sugar', artist: 'Harry Styles', plays: 68 },
    { title: 'Good 4 U', artist: 'Olivia Rodrigo', plays: 54 },
    { title: 'Stay', artist: 'The Kid LAROI & Justin Bieber', plays: 47 }
  ]
};

export function UserProfile() {
  const [user, setUser] = useState(mockUserData);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    name: user.name,
    bio: user.bio,
    location: user.location
  });

  const handleSaveProfile = () => {
    setUser({ ...user, ...editForm });
    setIsEditing(false);
  };

  const formatListeningTime = (hours) => {
    if (hours < 24) return `${hours} hours`;
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days} days, ${remainingHours} hours`;
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'playlist': return <Music className="h-4 w-4" />;
      case 'song': return <Heart className="h-4 w-4" />;
      case 'artist': return <User className="h-4 w-4" />;
      default: return <Music className="h-4 w-4" />;
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Profile Header */}
      <Card>
        <CardContent className="p-8">
          <div className="flex items-start gap-6">
            {/* Avatar */}
            <div className="relative">
              <Avatar className="w-32 h-32">
                <AvatarImage src={user.avatar} />
                <AvatarFallback className="text-2xl">
                  {user.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <Button
                size="sm"
                className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0"
                variant="secondary"
              >
                <Camera className="h-4 w-4" />
              </Button>
            </div>

            {/* Profile Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-3 mb-2">
                <Badge variant="secondary" className="bg-yellow-500/10 text-yellow-500">
                  Profile
                </Badge>
                {user.isPremium && (
                  <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white">
                    <Crown className="h-3 w-3 mr-1" />
                    Premium
                  </Badge>
                )}
              </div>

              {isEditing ? (
                <div className="space-y-4">
                  <Input
                    value={editForm.name}
                    onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                    className="text-2xl font-bold"
                  />
                  <Textarea
                    value={editForm.bio}
                    onChange={(e) => setEditForm({ ...editForm, bio: e.target.value })}
                    placeholder="Tell us about yourself..."
                    rows={3}
                  />
                  <Input
                    value={editForm.location}
                    onChange={(e) => setEditForm({ ...editForm, location: e.target.value })}
                    placeholder="Location"
                  />
                  <div className="flex gap-2">
                    <Button onClick={handleSaveProfile} size="sm">Save</Button>
                    <Button onClick={() => setIsEditing(false)} variant="outline" size="sm">Cancel</Button>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="flex items-center gap-3 mb-3">
                    <h1 className="text-4xl font-bold">{user.name}</h1>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsEditing(true)}
                      className="h-8 w-8 p-0"
                    >
                      <Edit3 className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-muted-foreground mb-2">{user.username}</p>
                  <p className="text-sm mb-3">{user.bio}</p>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{user.stats.followers} followers</span>
                    <span>•</span>
                    <span>{user.stats.following} following</span>
                    <span>•</span>
                    <span>{user.stats.playlistsCreated} playlists</span>
                    <span>•</span>
                    <span>Joined {new Date(user.joinDate).getFullYear()}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="stats">Statistics</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-500">{user.stats.songsPlayed.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Songs Played</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-500">{formatListeningTime(user.stats.totalListeningTime)}</div>
                <div className="text-sm text-muted-foreground">Listening Time</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-500">{user.stats.playlistsCreated}</div>
                <div className="text-sm text-muted-foreground">Playlists Created</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-orange-500">{user.stats.topGenre}</div>
                <div className="text-sm text-muted-foreground">Top Genre</div>
              </CardContent>
            </Card>
          </div>

          {/* Top Artists & Tracks */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Artists This Month</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {user.topArtists.map((artist, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <span className="text-sm text-muted-foreground w-6">{index + 1}</span>
                      <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-400 rounded-full" />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">{artist.name}</p>
                        <p className="text-sm text-muted-foreground">{artist.plays} plays</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Tracks This Month</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {user.topTracks.map((track, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <span className="text-sm text-muted-foreground w-6">{index + 1}</span>
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-400 rounded-md" />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">{track.title}</p>
                        <p className="text-sm text-muted-foreground truncate">{track.artist} • {track.plays} plays</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="stats" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Listening Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Daily Goal Progress</span>
                  <span>2.5 / 3 hours</span>
                </div>
                <Progress value={83} className="h-2" />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold">156</div>
                  <div className="text-sm text-muted-foreground">Songs this week</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold">23h</div>
                  <div className="text-sm text-muted-foreground">Hours this week</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {user.recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50">
                    <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm">
                        You <span className="font-medium">{activity.action}</span>{' '}
                        <span className="font-medium">{activity.item}</span>
                      </p>
                      <p className="text-xs text-muted-foreground">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Account Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Email</label>
                <Input value={user.email} disabled />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Username</label>
                <Input value={user.username} />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Location</label>
                <Input value={user.location} />
              </div>
              <Button>Save Changes</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Privacy Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Public Profile</p>
                  <p className="text-sm text-muted-foreground">Allow others to see your profile</p>
                </div>
                <Button variant="outline" size="sm">Toggle</Button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Activity Sharing</p>
                  <p className="text-sm text-muted-foreground">Share your listening activity with friends</p>
                </div>
                <Button variant="outline" size="sm">Toggle</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

