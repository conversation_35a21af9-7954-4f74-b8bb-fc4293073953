import React from 'react';
import { Play, Heart, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export function AlbumCard({ album, onPlay, onNavigate, className }) {
  const handlePlay = (e) => {
    e.stopPropagation();
    if (onPlay) {
      onPlay(album);
    }
  };

  const handleCardClick = () => {
    if (onNavigate) {
      onNavigate(album);
    }
  };

  return (
    <Card 
      className={`group hover:bg-muted/50 transition-all duration-200 cursor-pointer ${className}`}
      onClick={handleCardClick}
    >
      <CardContent className="p-4">
        <div className="relative">
          {/* Album Art */}
          <div className="aspect-square bg-muted rounded-lg overflow-hidden mb-4 relative">
            {album.coverArt ? (
              <img 
                src={album.coverArt} 
                alt={album.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-green-500 to-emerald-400" />
            )}
            
            {/* Play Button Overlay */}
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
              <Button
                variant="default"
                size="lg"
                onClick={handlePlay}
                className="rounded-full bg-green-500 hover:bg-green-600 text-white shadow-lg transform scale-90 group-hover:scale-100 transition-transform"
              >
                <Play className="h-6 w-6 ml-1" />
              </Button>
            </div>
          </div>

          {/* Album Info */}
          <div className="space-y-1">
            <h3 className="font-semibold text-sm truncate group-hover:text-green-500 transition-colors">
              {album.title}
            </h3>
            <p className="text-xs text-muted-foreground truncate">
              {album.artist}
            </p>
            <p className="text-xs text-muted-foreground">
              {album.year} • {album.trackCount} songs
            </p>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Heart className="h-4 w-4" />
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Play album</DropdownMenuItem>
                <DropdownMenuItem>Add to queue</DropdownMenuItem>
                <DropdownMenuItem>Add to playlist</DropdownMenuItem>
                <DropdownMenuItem>Go to artist</DropdownMenuItem>
                <DropdownMenuItem>Share</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

