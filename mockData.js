// Mock data for the Spotify 2.0 app
export const mockTracks = [
  {
    id: 1,
    title: 'Blinding Lights',
    artist: 'The Weeknd',
    album: 'After Hours',
    duration: 200,
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Sample audio for testing
    albumArt: null,
    year: 2020,
    genre: 'Pop'
  },
  {
    id: 2,
    title: 'Watermelon Sugar',
    artist: '<PERSON>',
    album: 'Fine Line',
    duration: 174,
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    albumArt: null,
    year: 2019,
    genre: 'Pop'
  },
  {
    id: 3,
    title: 'Levitating',
    artist: '<PERSON><PERSON>',
    album: 'Future Nostalgia',
    duration: 203,
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    albumArt: null,
    year: 2020,
    genre: 'Pop'
  },
  {
    id: 4,
    title: 'Good 4 U',
    artist: '<PERSON>',
    album: 'SOUR',
    duration: 178,
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    albumArt: null,
    year: 2021,
    genre: 'Pop'
  },
  {
    id: 5,
    title: 'Stay',
    artist: 'The Kid LAROI & Justin <PERSON>',
    album: 'F*CK LOVE 3: OVER YOU',
    duration: 141,
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    albumArt: null,
    year: 2021,
    genre: 'Pop'
  }
];

export const mockAlbums = [
  {
    id: 1,
    title: 'After Hours',
    artist: 'The Weeknd',
    year: 2020,
    trackCount: 14,
    coverArt: null,
    tracks: [1, 2, 3] // Track IDs
  },
  {
    id: 2,
    title: 'Future Nostalgia',
    artist: 'Dua Lipa',
    year: 2020,
    trackCount: 11,
    coverArt: null,
    tracks: [3, 4, 5]
  },
  {
    id: 3,
    title: 'Fine Line',
    artist: 'Harry Styles',
    year: 2019,
    trackCount: 12,
    coverArt: null,
    tracks: [2, 4, 5]
  },
  {
    id: 4,
    title: 'SOUR',
    artist: 'Olivia Rodrigo',
    year: 2021,
    trackCount: 11,
    coverArt: null,
    tracks: [4, 5, 1]
  }
];

export const mockPlaylists = [
  {
    id: 1,
    name: 'Today\'s Top Hits',
    description: 'The most played songs right now',
    trackCount: 50,
    creator: 'Spotify',
    coverArt: null,
    isPublic: true,
    isCollaborative: false,
    isOwner: false,
    tracks: [1, 2, 3, 4, 5]
  },
  {
    id: 2,
    name: 'Chill Hits',
    description: 'Kick back to the best new and recent chill hits',
    trackCount: 75,
    creator: 'Spotify',
    coverArt: null,
    isPublic: true,
    isCollaborative: false,
    isOwner: false,
    tracks: [2, 3, 5]
  },
  {
    id: 3,
    name: 'My Awesome Playlist',
    description: 'My personal favorite tracks',
    trackCount: 25,
    creator: 'John Doe',
    coverArt: null,
    isPublic: false,
    isCollaborative: true,
    isOwner: true,
    tracks: [1, 3, 4]
  },
  {
    id: 4,
    name: 'Workout Motivation',
    description: 'High-energy tracks to power your workout',
    trackCount: 100,
    creator: 'Spotify',
    coverArt: null,
    isPublic: true,
    isCollaborative: false,
    isOwner: false,
    tracks: [1, 4, 5]
  }
];

export const mockArtists = [
  {
    id: 1,
    name: 'The Weeknd',
    genre: 'Pop',
    followers: 85000000,
    verified: true,
    topTracks: [1, 2],
    albums: [1]
  },
  {
    id: 2,
    name: 'Dua Lipa',
    genre: 'Pop',
    followers: 65000000,
    verified: true,
    topTracks: [3, 5],
    albums: [2]
  },
  {
    id: 3,
    name: 'Harry Styles',
    genre: 'Pop',
    followers: 45000000,
    verified: true,
    topTracks: [2, 4],
    albums: [3]
  },
  {
    id: 4,
    name: 'Olivia Rodrigo',
    genre: 'Pop',
    followers: 35000000,
    verified: true,
    topTracks: [4, 5],
    albums: [4]
  }
];

// Helper functions to get data
export const getTrackById = (id) => mockTracks.find(track => track.id === id);
export const getAlbumById = (id) => mockAlbums.find(album => album.id === id);
export const getPlaylistById = (id) => mockPlaylists.find(playlist => playlist.id === id);
export const getArtistById = (id) => mockArtists.find(artist => artist.id === id);

export const getTracksByIds = (ids) => ids.map(id => getTrackById(id)).filter(Boolean);

// Search function
export const searchTracks = (query) => {
  if (!query) return [];
  const lowercaseQuery = query.toLowerCase();
  return mockTracks.filter(track => 
    track.title.toLowerCase().includes(lowercaseQuery) ||
    track.artist.toLowerCase().includes(lowercaseQuery) ||
    track.album.toLowerCase().includes(lowercaseQuery)
  );
};

export const searchAlbums = (query) => {
  if (!query) return [];
  const lowercaseQuery = query.toLowerCase();
  return mockAlbums.filter(album => 
    album.title.toLowerCase().includes(lowercaseQuery) ||
    album.artist.toLowerCase().includes(lowercaseQuery)
  );
};

export const searchPlaylists = (query) => {
  if (!query) return [];
  const lowercaseQuery = query.toLowerCase();
  return mockPlaylists.filter(playlist => 
    playlist.name.toLowerCase().includes(lowercaseQuery) ||
    playlist.description.toLowerCase().includes(lowercaseQuery)
  );
};

export const searchArtists = (query) => {
  if (!query) return [];
  const lowercaseQuery = query.toLowerCase();
  return mockArtists.filter(artist => 
    artist.name.toLowerCase().includes(lowercaseQuery)
  );
};

