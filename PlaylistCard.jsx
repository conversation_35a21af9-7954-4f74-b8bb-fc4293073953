import React from 'react';
import { Play, Heart, MoreHorizontal, Music, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export function PlaylistCard({ playlist, onPlay, onNavigate, className }) {
  const handlePlay = (e) => {
    e.stopPropagation();
    if (onPlay) {
      onPlay(playlist);
    }
  };

  const handleCardClick = () => {
    if (onNavigate) {
      onNavigate(playlist);
    }
  };

  return (
    <Card 
      className={`group hover:bg-muted/50 transition-all duration-200 cursor-pointer ${className}`}
      onClick={handleCardClick}
    >
      <CardContent className="p-4">
        <div className="relative">
          {/* Playlist Cover */}
          <div className="aspect-square bg-muted rounded-lg overflow-hidden mb-4 relative">
            {playlist.coverArt ? (
              <img 
                src={playlist.coverArt} 
                alt={playlist.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-purple-500 to-pink-400 flex items-center justify-center">
                <Music className="h-12 w-12 text-white/80" />
              </div>
            )}
            
            {/* Play Button Overlay */}
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
              <Button
                variant="default"
                size="lg"
                onClick={handlePlay}
                className="rounded-full bg-green-500 hover:bg-green-600 text-white shadow-lg transform scale-90 group-hover:scale-100 transition-transform"
              >
                <Play className="h-6 w-6 ml-1" />
              </Button>
            </div>
          </div>

          {/* Playlist Info */}
          <div className="space-y-2">
            <h3 className="font-semibold text-sm truncate group-hover:text-green-500 transition-colors">
              {playlist.name}
            </h3>
            
            {playlist.description && (
              <p className="text-xs text-muted-foreground line-clamp-2">
                {playlist.description}
              </p>
            )}
            
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>{playlist.trackCount} songs</span>
              {playlist.isCollaborative && (
                <>
                  <span>•</span>
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    <span>Collaborative</span>
                  </div>
                </>
              )}
              {playlist.isPublic === false && (
                <>
                  <span>•</span>
                  <Badge variant="secondary" className="text-xs px-1 py-0">
                    Private
                  </Badge>
                </>
              )}
            </div>
            
            {playlist.creator && (
              <p className="text-xs text-muted-foreground">
                By {playlist.creator}
              </p>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Heart className="h-4 w-4" />
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Play playlist</DropdownMenuItem>
                <DropdownMenuItem>Add to queue</DropdownMenuItem>
                <DropdownMenuItem>Follow playlist</DropdownMenuItem>
                <DropdownMenuItem>Go to creator</DropdownMenuItem>
                <DropdownMenuItem>Share</DropdownMenuItem>
                {playlist.isOwner && (
                  <>
                    <DropdownMenuItem>Edit details</DropdownMenuItem>
                    <DropdownMenuItem className="text-destructive">
                      Delete playlist
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

