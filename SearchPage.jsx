import React, { useState, useEffect } from 'react';
import { Search as SearchIcon, TrendingUp, Clock } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { TrackCard } from '@/components/UI/TrackCard';
import { AlbumCard } from '@/components/UI/AlbumCard';
import { PlaylistCard } from '@/components/UI/PlaylistCard';
import { 
  searchTracks, 
  searchAlbums, 
  searchPlaylists, 
  searchArtists,
  mockTracks,
  mockAlbums,
  mockPlaylists 
} from '@/utils/mockData';
import { usePlayerDispatch, playerActions } from '@/contexts/PlayerContext';

export function SearchPage() {
  const [query, setQuery] = useState('');
  const [searchResults, setSearchResults] = useState({
    tracks: [],
    albums: [],
    playlists: [],
    artists: []
  });
  const [recentSearches, setRecentSearches] = useState([
    'Billie Eilish',
    'The Weeknd',
    'Taylor Swift',
    'Drake'
  ]);
  const [trendingSearches] = useState([
    'New releases 2024',
    'Chill playlist',
    'Workout music',
    'Lo-fi beats',
    'Pop hits',
    'Indie rock'
  ]);

  const dispatch = usePlayerDispatch();

  useEffect(() => {
    if (query.trim()) {
      const results = {
        tracks: searchTracks(query),
        albums: searchAlbums(query),
        playlists: searchPlaylists(query),
        artists: searchArtists(query)
      };
      setSearchResults(results);
    } else {
      setSearchResults({
        tracks: [],
        albums: [],
        playlists: [],
        artists: []
      });
    }
  }, [query]);

  const handleSearchChange = (e) => {
    setQuery(e.target.value);
  };

  const handleSearchSelect = (searchTerm) => {
    setQuery(searchTerm);
    
    // Add to recent searches if not already there
    if (!recentSearches.includes(searchTerm)) {
      setRecentSearches(prev => [searchTerm, ...prev.slice(0, 3)]);
    }
  };

  const handlePlayTrack = (track) => {
    dispatch(playerActions.setCurrentTrack(track));
    dispatch(playerActions.setQueue([track]));
    dispatch(playerActions.setCurrentIndex(0));
    if (!track.isPlaying) {
      dispatch(playerActions.togglePlay());
    }
  };

  const handlePlayAlbum = (album) => {
    console.log('Playing album:', album);
    // In a real app, you would load the album tracks and start playing
  };

  const handlePlayPlaylist = (playlist) => {
    console.log('Playing playlist:', playlist);
    // In a real app, you would load the playlist tracks and start playing
  };

  const hasResults = Object.values(searchResults).some(results => results.length > 0);

  return (
    <div className="p-6 space-y-6">
      {/* Search Header */}
      <div className="space-y-4">
        <h1 className="text-3xl font-bold">Search</h1>
        
        {/* Search Input */}
        <div className="relative max-w-md">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="What do you want to listen to?"
            value={query}
            onChange={handleSearchChange}
            className="pl-10 bg-muted/50 border-none focus:bg-background text-lg h-12"
          />
        </div>
      </div>

      {/* Search Results */}
      {query && hasResults && (
        <div className="space-y-6">
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="tracks">Songs</TabsTrigger>
              <TabsTrigger value="albums">Albums</TabsTrigger>
              <TabsTrigger value="playlists">Playlists</TabsTrigger>
              <TabsTrigger value="artists">Artists</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-6">
              {/* Top Result */}
              {searchResults.tracks.length > 0 && (
                <section>
                  <h2 className="text-2xl font-bold mb-4">Top result</h2>
                  <Card className="max-w-sm">
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-400 rounded-lg" />
                        <div>
                          <h3 className="text-xl font-bold">{searchResults.tracks[0].title}</h3>
                          <p className="text-muted-foreground">{searchResults.tracks[0].artist}</p>
                          <Badge variant="secondary" className="mt-2">Song</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </section>
              )}

              {/* Songs */}
              {searchResults.tracks.length > 0 && (
                <section>
                  <h2 className="text-2xl font-bold mb-4">Songs</h2>
                  <div className="space-y-2">
                    {searchResults.tracks.slice(0, 4).map((track, index) => (
                      <TrackCard
                        key={track.id}
                        track={track}
                        index={index}
                        onPlay={handlePlayTrack}
                      />
                    ))}
                  </div>
                </section>
              )}

              {/* Albums */}
              {searchResults.albums.length > 0 && (
                <section>
                  <h2 className="text-2xl font-bold mb-4">Albums</h2>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                    {searchResults.albums.slice(0, 5).map((album) => (
                      <AlbumCard
                        key={album.id}
                        album={album}
                        onPlay={handlePlayAlbum}
                      />
                    ))}
                  </div>
                </section>
              )}

              {/* Playlists */}
              {searchResults.playlists.length > 0 && (
                <section>
                  <h2 className="text-2xl font-bold mb-4">Playlists</h2>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                    {searchResults.playlists.slice(0, 5).map((playlist) => (
                      <PlaylistCard
                        key={playlist.id}
                        playlist={playlist}
                        onPlay={handlePlayPlaylist}
                      />
                    ))}
                  </div>
                </section>
              )}
            </TabsContent>

            <TabsContent value="tracks">
              <div className="space-y-2">
                {searchResults.tracks.map((track, index) => (
                  <TrackCard
                    key={track.id}
                    track={track}
                    index={index}
                    showIndex={true}
                    onPlay={handlePlayTrack}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="albums">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                {searchResults.albums.map((album) => (
                  <AlbumCard
                    key={album.id}
                    album={album}
                    onPlay={handlePlayAlbum}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="playlists">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                {searchResults.playlists.map((playlist) => (
                  <PlaylistCard
                    key={playlist.id}
                    playlist={playlist}
                    onPlay={handlePlayPlaylist}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="artists">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                {searchResults.artists.map((artist) => (
                  <Card key={artist.id} className="group hover:bg-muted/50 transition-colors cursor-pointer">
                    <CardContent className="p-4 text-center">
                      <div className="w-32 h-32 mx-auto bg-gradient-to-br from-orange-500 to-red-400 rounded-full mb-4" />
                      <h3 className="font-semibold group-hover:text-green-500 transition-colors">{artist.name}</h3>
                      <p className="text-sm text-muted-foreground">Artist</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      )}

      {/* No Results */}
      {query && !hasResults && (
        <div className="text-center py-12">
          <SearchIcon className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-2">No results found for "{query}"</h2>
          <p className="text-muted-foreground">Please make sure your words are spelled correctly, or use fewer or different keywords.</p>
        </div>
      )}

      {/* Browse Categories (when no search) */}
      {!query && (
        <div className="space-y-6">
          {/* Recent Searches */}
          {recentSearches.length > 0 && (
            <section>
              <div className="flex items-center gap-2 mb-4">
                <Clock className="h-5 w-5 text-muted-foreground" />
                <h2 className="text-xl font-bold">Recent searches</h2>
              </div>
              <div className="space-y-2">
                {recentSearches.map((search, index) => (
                  <Card 
                    key={index} 
                    className="cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => handleSearchSelect(search)}
                  >
                    <CardContent className="p-3 flex items-center gap-3">
                      <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center">
                        <SearchIcon className="h-5 w-5 text-muted-foreground" />
                      </div>
                      <span className="font-medium">{search}</span>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>
          )}

          {/* Trending Searches */}
          <section>
            <div className="flex items-center gap-2 mb-4">
              <TrendingUp className="h-5 w-5 text-muted-foreground" />
              <h2 className="text-xl font-bold">Trending searches</h2>
            </div>
            <div className="flex flex-wrap gap-2">
              {trendingSearches.map((search, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="cursor-pointer hover:bg-accent text-sm py-2 px-4"
                  onClick={() => handleSearchSelect(search)}
                >
                  {search}
                </Badge>
              ))}
            </div>
          </section>

          {/* Browse All */}
          <section>
            <h2 className="text-2xl font-bold mb-4">Browse all</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {[
                { name: 'Pop', color: 'from-pink-500 to-rose-400' },
                { name: 'Hip-Hop', color: 'from-purple-500 to-indigo-400' },
                { name: 'Rock', color: 'from-red-500 to-orange-400' },
                { name: 'Jazz', color: 'from-blue-500 to-cyan-400' },
                { name: 'Classical', color: 'from-emerald-500 to-teal-400' },
                { name: 'Electronic', color: 'from-violet-500 to-purple-400' },
                { name: 'Country', color: 'from-amber-500 to-yellow-400' },
                { name: 'R&B', color: 'from-rose-500 to-pink-400' },
              ].map((genre, index) => (
                <Card key={index} className="group hover:scale-105 transition-transform cursor-pointer">
                  <CardContent className="p-0">
                    <div className={`h-32 bg-gradient-to-br ${genre.color} rounded-lg flex items-center justify-center`}>
                      <h3 className="text-white text-xl font-bold">{genre.name}</h3>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        </div>
      )}
    </div>
  );
}

