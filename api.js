// API service layer for Spotify 2.0
import { mockTracks, mockAlbums, mockPlaylists, mockArtists } from './mockData';

// Base API configuration
const API_CONFIG = {
  baseURL: process.env.REACT_APP_API_URL || 'https://api.spotify2.app',
  timeout: 10000,
  retries: 3
};

// HTTP client with retry logic
class APIClient {
  constructor(config = {}) {
    this.config = { ...API_CONFIG, ...config };
  }

  async request(endpoint, options = {}) {
    const url = `${this.config.baseURL}${endpoint}`;
    const config = {
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    // Add authentication if available
    const token = localStorage.getItem('spotify2_auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    let lastError;
    for (let i = 0; i < this.config.retries; i++) {
      try {
        const response = await fetch(url, config);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return { data, status: response.status };
      } catch (error) {
        lastError = error;
        if (i < this.config.retries - 1) {
          await this.delay(Math.pow(2, i) * 1000); // Exponential backoff
        }
      }
    }

    throw lastError;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.request(url, { method: 'GET' });
  }

  post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }
}

const apiClient = new APIClient();

// Mock API service (fallback when real API is not available)
class MockAPIService {
  async delay(ms = 500) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async search(query, type = 'all', limit = 20) {
    await this.delay();
    
    const results = {
      tracks: [],
      albums: [],
      artists: [],
      playlists: []
    };

    if (type === 'all' || type === 'tracks') {
      results.tracks = mockTracks
        .filter(track => 
          track.title.toLowerCase().includes(query.toLowerCase()) ||
          track.artist.toLowerCase().includes(query.toLowerCase())
        )
        .slice(0, limit);
    }

    if (type === 'all' || type === 'albums') {
      results.albums = mockAlbums
        .filter(album => 
          album.title.toLowerCase().includes(query.toLowerCase()) ||
          album.artist.toLowerCase().includes(query.toLowerCase())
        )
        .slice(0, limit);
    }

    if (type === 'all' || type === 'artists') {
      results.artists = mockArtists
        .filter(artist => 
          artist.name.toLowerCase().includes(query.toLowerCase())
        )
        .slice(0, limit);
    }

    if (type === 'all' || type === 'playlists') {
      results.playlists = mockPlaylists
        .filter(playlist => 
          playlist.name.toLowerCase().includes(query.toLowerCase())
        )
        .slice(0, limit);
    }

    return results;
  }

  async getTrack(id) {
    await this.delay();
    return mockTracks.find(track => track.id === parseInt(id));
  }

  async getAlbum(id) {
    await this.delay();
    return mockAlbums.find(album => album.id === parseInt(id));
  }

  async getPlaylist(id) {
    await this.delay();
    return mockPlaylists.find(playlist => playlist.id === parseInt(id));
  }

  async getArtist(id) {
    await this.delay();
    return mockArtists.find(artist => artist.id === parseInt(id));
  }

  async getFeaturedPlaylists(limit = 10) {
    await this.delay();
    return mockPlaylists.slice(0, limit);
  }

  async getNewReleases(limit = 10) {
    await this.delay();
    return mockAlbums.slice(0, limit);
  }

  async getTopTracks(limit = 20) {
    await this.delay();
    return mockTracks.slice(0, limit);
  }

  async getRecommendations(seedTracks = [], seedArtists = [], seedGenres = [], limit = 20) {
    await this.delay();
    // Simple recommendation logic based on seeds
    return mockTracks
      .filter(track => !seedTracks.includes(track.id))
      .slice(0, limit);
  }

  async getUserProfile() {
    await this.delay();
    return {
      id: 'user123',
      name: 'John Doe',
      email: '<EMAIL>',
      country: 'US',
      followers: 156,
      following: 89,
      premium: true
    };
  }

  async getUserPlaylists() {
    await this.delay();
    return mockPlaylists.filter(playlist => playlist.isOwner);
  }

  async createPlaylist(data) {
    await this.delay();
    const newPlaylist = {
      id: Date.now(),
      name: data.name,
      description: data.description || '',
      isPublic: data.public || false,
      tracks: [],
      creator: 'John Doe',
      isOwner: true,
      trackCount: 0,
      createdAt: new Date().toISOString()
    };
    return newPlaylist;
  }

  async addToPlaylist(playlistId, trackIds) {
    await this.delay();
    return { success: true, added: trackIds.length };
  }

  async removeFromPlaylist(playlistId, trackIds) {
    await this.delay();
    return { success: true, removed: trackIds.length };
  }

  async likeTrack(trackId) {
    await this.delay();
    return { success: true, liked: true };
  }

  async unlikeTrack(trackId) {
    await this.delay();
    return { success: true, liked: false };
  }

  async followArtist(artistId) {
    await this.delay();
    return { success: true, following: true };
  }

  async unfollowArtist(artistId) {
    await this.delay();
    return { success: true, following: false };
  }

  async getAudioFeatures(trackId) {
    await this.delay();
    return {
      danceability: Math.random(),
      energy: Math.random(),
      valence: Math.random(),
      acousticness: Math.random(),
      instrumentalness: Math.random(),
      liveness: Math.random(),
      speechiness: Math.random(),
      tempo: 120 + Math.random() * 60
    };
  }
}

// Real API service (to be implemented when backend is ready)
class RealAPIService {
  async search(query, type = 'all', limit = 20) {
    try {
      const response = await apiClient.get('/search', { q: query, type, limit });
      return response.data;
    } catch (error) {
      console.error('Search API error:', error);
      throw error;
    }
  }

  async getTrack(id) {
    try {
      const response = await apiClient.get(`/tracks/${id}`);
      return response.data;
    } catch (error) {
      console.error('Get track API error:', error);
      throw error;
    }
  }

  async getAlbum(id) {
    try {
      const response = await apiClient.get(`/albums/${id}`);
      return response.data;
    } catch (error) {
      console.error('Get album API error:', error);
      throw error;
    }
  }

  async getPlaylist(id) {
    try {
      const response = await apiClient.get(`/playlists/${id}`);
      return response.data;
    } catch (error) {
      console.error('Get playlist API error:', error);
      throw error;
    }
  }

  async getUserProfile() {
    try {
      const response = await apiClient.get('/me');
      return response.data;
    } catch (error) {
      console.error('Get user profile API error:', error);
      throw error;
    }
  }

  async createPlaylist(data) {
    try {
      const response = await apiClient.post('/playlists', data);
      return response.data;
    } catch (error) {
      console.error('Create playlist API error:', error);
      throw error;
    }
  }

  // Add more real API methods as needed...
}

// API service factory
class APIService {
  constructor() {
    this.mockService = new MockAPIService();
    this.realService = new RealAPIService();
    this.useMockAPI = process.env.NODE_ENV === 'development' || !process.env.REACT_APP_API_URL;
  }

  get service() {
    return this.useMockAPI ? this.mockService : this.realService;
  }

  // Proxy all methods to the active service
  async search(...args) {
    return this.service.search(...args);
  }

  async getTrack(...args) {
    return this.service.getTrack(...args);
  }

  async getAlbum(...args) {
    return this.service.getAlbum(...args);
  }

  async getPlaylist(...args) {
    return this.service.getPlaylist(...args);
  }

  async getArtist(...args) {
    return this.service.getArtist(...args);
  }

  async getFeaturedPlaylists(...args) {
    return this.service.getFeaturedPlaylists(...args);
  }

  async getNewReleases(...args) {
    return this.service.getNewReleases(...args);
  }

  async getTopTracks(...args) {
    return this.service.getTopTracks(...args);
  }

  async getRecommendations(...args) {
    return this.service.getRecommendations(...args);
  }

  async getUserProfile(...args) {
    return this.service.getUserProfile(...args);
  }

  async getUserPlaylists(...args) {
    return this.service.getUserPlaylists(...args);
  }

  async createPlaylist(...args) {
    return this.service.createPlaylist(...args);
  }

  async addToPlaylist(...args) {
    return this.service.addToPlaylist(...args);
  }

  async removeFromPlaylist(...args) {
    return this.service.removeFromPlaylist(...args);
  }

  async likeTrack(...args) {
    return this.service.likeTrack(...args);
  }

  async unlikeTrack(...args) {
    return this.service.unlikeTrack(...args);
  }

  async followArtist(...args) {
    return this.service.followArtist(...args);
  }

  async unfollowArtist(...args) {
    return this.service.unfollowArtist(...args);
  }

  async getAudioFeatures(...args) {
    return this.service.getAudioFeatures(...args);
  }

  // Switch between mock and real API
  setUseMockAPI(useMock) {
    this.useMockAPI = useMock;
  }

  // Health check
  async healthCheck() {
    try {
      if (this.useMockAPI) {
        return { status: 'ok', service: 'mock' };
      } else {
        const response = await apiClient.get('/health');
        return { status: 'ok', service: 'real', ...response.data };
      }
    } catch (error) {
      return { status: 'error', error: error.message };
    }
  }
}

// Export singleton instance
export const apiService = new APIService();
export default apiService;

