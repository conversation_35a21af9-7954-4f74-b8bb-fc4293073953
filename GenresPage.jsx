import React, { useState } from 'react';
import { Music, TrendingUp, Clock, Play } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { PlaylistCard } from '@/components/UI/PlaylistCard';
import { AlbumCard } from '@/components/UI/AlbumCard';

const genres = [
  {
    id: 'pop',
    name: 'Pop',
    description: 'The most popular music right now',
    color: 'from-pink-500 to-rose-400',
    trackCount: 15420,
    trending: true
  },
  {
    id: 'hip-hop',
    name: 'Hip-Hop',
    description: 'Beats, rhymes, and urban culture',
    color: 'from-purple-500 to-indigo-400',
    trackCount: 12350,
    trending: true
  },
  {
    id: 'rock',
    name: 'Rock',
    description: 'Classic and modern rock music',
    color: 'from-red-500 to-orange-400',
    trackCount: 18750,
    trending: false
  },
  {
    id: 'electronic',
    name: 'Electronic',
    description: 'EDM, house, techno, and more',
    color: 'from-cyan-500 to-blue-400',
    trackCount: 9870,
    trending: true
  },
  {
    id: 'jazz',
    name: 'Jazz',
    description: 'Smooth jazz and contemporary',
    color: 'from-amber-500 to-yellow-400',
    trackCount: 6540,
    trending: false
  },
  {
    id: 'classical',
    name: 'Classical',
    description: 'Timeless orchestral masterpieces',
    color: 'from-emerald-500 to-teal-400',
    trackCount: 8920,
    trending: false
  },
  {
    id: 'country',
    name: 'Country',
    description: 'Modern and classic country hits',
    color: 'from-orange-500 to-red-400',
    trackCount: 7650,
    trending: false
  },
  {
    id: 'rb',
    name: 'R&B',
    description: 'Rhythm, blues, and soul',
    color: 'from-violet-500 to-purple-400',
    trackCount: 5430,
    trending: true
  },
  {
    id: 'indie',
    name: 'Indie',
    description: 'Independent and alternative music',
    color: 'from-teal-500 to-cyan-400',
    trackCount: 11200,
    trending: true
  },
  {
    id: 'latin',
    name: 'Latin',
    description: 'Reggaeton, salsa, and Latin pop',
    color: 'from-rose-500 to-pink-400',
    trackCount: 8760,
    trending: true
  },
  {
    id: 'folk',
    name: 'Folk',
    description: 'Acoustic and traditional folk',
    color: 'from-green-500 to-emerald-400',
    trackCount: 4320,
    trending: false
  },
  {
    id: 'reggae',
    name: 'Reggae',
    description: 'Jamaican rhythms and culture',
    color: 'from-yellow-500 to-orange-400',
    trackCount: 3210,
    trending: false
  }
];

const mockGenrePlaylists = {
  pop: [
    { id: 1, name: 'Pop Hits 2024', description: 'The biggest pop songs', trackCount: 50, creator: 'Spotify' },
    { id: 2, name: 'Pop Rising', description: 'Up-and-coming pop artists', trackCount: 30, creator: 'Spotify' },
    { id: 3, name: 'Throwback Pop', description: '2000s and 2010s pop favorites', trackCount: 75, creator: 'Spotify' }
  ],
  'hip-hop': [
    { id: 4, name: 'RapCaviar', description: 'The best in hip-hop', trackCount: 65, creator: 'Spotify' },
    { id: 5, name: 'Hip-Hop Central', description: 'The pulse of hip-hop culture', trackCount: 40, creator: 'Spotify' },
    { id: 6, name: 'Underground Hip-Hop', description: 'Discover new voices', trackCount: 35, creator: 'Spotify' }
  ]
};

export function GenresPage() {
  const [selectedGenre, setSelectedGenre] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredGenres = genres.filter(genre =>
    genre.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    genre.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleGenreSelect = (genre) => {
    setSelectedGenre(genre);
  };

  const handleBackToGenres = () => {
    setSelectedGenre(null);
  };

  const handlePlayGenre = (genre) => {
    console.log('Playing genre:', genre.name);
    // In a real app, this would start playing a genre radio or playlist
  };

  if (selectedGenre) {
    const playlists = mockGenrePlaylists[selectedGenre.id] || [];
    
    return (
      <div className="p-6 space-y-6">
        {/* Genre Header */}
        <div className={`bg-gradient-to-br ${selectedGenre.color} rounded-lg p-8 text-white`}>
          <Button
            variant="ghost"
            onClick={handleBackToGenres}
            className="text-white hover:bg-white/20 mb-4"
          >
            ← Back to Genres
          </Button>
          <div className="flex items-end gap-6">
            <div className="w-32 h-32 bg-white/20 rounded-lg flex items-center justify-center">
              <Music className="h-16 w-16" />
            </div>
            <div>
              <Badge variant="secondary" className="mb-2 bg-white/20 text-white">
                Genre
              </Badge>
              <h1 className="text-5xl font-bold mb-2">{selectedGenre.name}</h1>
              <p className="text-lg opacity-90 mb-4">{selectedGenre.description}</p>
              <p className="opacity-75">{selectedGenre.trackCount.toLocaleString()} songs available</p>
            </div>
          </div>
        </div>

        {/* Play Button */}
        <div>
          <Button
            onClick={() => handlePlayGenre(selectedGenre)}
            size="lg"
            className="w-14 h-14 rounded-full bg-green-500 hover:bg-green-600 hover:scale-105 transition-all"
          >
            <Play className="h-6 w-6 ml-1" />
          </Button>
        </div>

        {/* Featured Playlists */}
        <div>
          <h2 className="text-2xl font-bold mb-4">Featured {selectedGenre.name} Playlists</h2>
          {playlists.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {playlists.map((playlist) => (
                <PlaylistCard
                  key={playlist.id}
                  playlist={playlist}
                  onPlay={() => console.log('Playing playlist:', playlist.name)}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Music className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No playlists available</h3>
              <p className="text-muted-foreground">Check back later for curated {selectedGenre.name} playlists</p>
            </div>
          )}
        </div>

        {/* Popular Artists in Genre */}
        <div>
          <h2 className="text-2xl font-bold mb-4">Popular {selectedGenre.name} Artists</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Card key={i} className="group hover:bg-muted/50 transition-colors cursor-pointer">
                <CardContent className="p-4 text-center">
                  <div className={`w-24 h-24 mx-auto mb-3 rounded-full bg-gradient-to-br ${selectedGenre.color}`} />
                  <h3 className="font-semibold group-hover:text-green-500 transition-colors">Artist {i}</h3>
                  <p className="text-sm text-muted-foreground">Artist</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold mb-2">Browse by Genre</h1>
        <p className="text-muted-foreground">
          Discover music across different genres and styles
        </p>
      </div>

      {/* Search */}
      <div className="max-w-md">
        <Input
          type="text"
          placeholder="Search genres..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="bg-muted/50 border-none focus:bg-background"
        />
      </div>

      {/* Trending Genres */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <TrendingUp className="h-5 w-5 text-green-500" />
          <h2 className="text-xl font-bold">Trending Now</h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {filteredGenres.filter(genre => genre.trending).map((genre) => (
            <Card
              key={genre.id}
              className="group hover:scale-105 transition-transform cursor-pointer"
              onClick={() => handleGenreSelect(genre)}
            >
              <CardContent className="p-0">
                <div className={`h-32 bg-gradient-to-br ${genre.color} rounded-lg flex items-center justify-center relative overflow-hidden`}>
                  <h3 className="text-white text-xl font-bold z-10">{genre.name}</h3>
                  <div className="absolute top-2 right-2">
                    <Badge variant="secondary" className="bg-white/20 text-white text-xs">
                      Trending
                    </Badge>
                  </div>
                  <Music className="absolute -bottom-2 -right-2 h-16 w-16 text-white/20 transform rotate-12" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* All Genres */}
      <div>
        <h2 className="text-xl font-bold mb-4">All Genres</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {filteredGenres.map((genre) => (
            <Card
              key={genre.id}
              className="group hover:scale-105 transition-transform cursor-pointer"
              onClick={() => handleGenreSelect(genre)}
            >
              <CardContent className="p-0">
                <div className={`h-24 bg-gradient-to-br ${genre.color} rounded-lg flex items-center justify-center relative overflow-hidden`}>
                  <h3 className="text-white text-lg font-bold z-10">{genre.name}</h3>
                  {genre.trending && (
                    <div className="absolute top-1 right-1">
                      <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" />
                    </div>
                  )}
                </div>
                <div className="p-3">
                  <p className="text-xs text-muted-foreground truncate">{genre.description}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    {genre.trackCount.toLocaleString()} songs
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Recently Played Genres */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Clock className="h-5 w-5 text-muted-foreground" />
          <h2 className="text-xl font-bold">Recently Played</h2>
        </div>
        <div className="flex gap-4 overflow-x-auto pb-4">
          {genres.slice(0, 6).map((genre) => (
            <Card
              key={genre.id}
              className="flex-shrink-0 w-48 group hover:bg-muted/50 transition-colors cursor-pointer"
              onClick={() => handleGenreSelect(genre)}
            >
              <CardContent className="p-4">
                <div className={`w-16 h-16 bg-gradient-to-br ${genre.color} rounded-lg mb-3`} />
                <h3 className="font-semibold group-hover:text-green-500 transition-colors">{genre.name}</h3>
                <p className="text-sm text-muted-foreground truncate">{genre.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}

