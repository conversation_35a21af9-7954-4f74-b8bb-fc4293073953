import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { PlayerProvider } from '@/contexts/PlayerContext';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { Sidebar } from '@/components/Sidebar/Sidebar';
import { TopNavigation } from '@/components/TopNavigation';
import { MiniPlayer } from '@/components/Player/MiniPlayer';
import { HomePage } from '@/pages/HomePage';
import { SearchPage } from '@/pages/SearchPage';
import { LibraryPage } from '@/pages/LibraryPage';
import { PlaylistView } from '@/pages/PlaylistView';
import { AlbumView } from '@/pages/AlbumView';
import { UserProfile } from '@/pages/UserProfile';
import { GenresPage } from '@/pages/GenresPage';
import { FocusMode } from '@/components/FocusMode';
import { MoodPlaylistGenerator } from '@/components/MoodPlaylistGenerator';
import { SocialFeatures } from '@/components/SocialFeatures';
import { LyricsDisplay } from '@/components/Player/LyricsDisplay';
import { AudioVisualizer } from '@/components/Player/AudioVisualizer';
import './App.css';

function App() {
  return (
    <ThemeProvider>
      <PlayerProvider>
        <Router>
          <div className="h-screen flex flex-col bg-background text-foreground">
            {/* Main Layout */}
            <div className="flex flex-1 overflow-hidden">
              {/* Sidebar */}
              <Sidebar />
              
              {/* Main Content Area */}
              <div className="flex-1 flex flex-col">
                {/* Top Navigation */}
                <TopNavigation />
                
                {/* Page Content */}
                <main className="flex-1 overflow-y-auto">
                  <Routes>
                    <Route path="/" element={<HomePage />} />
                    <Route path="/search" element={<SearchPage />} />
                    <Route path="/library" element={<LibraryPage />} />
                    <Route path="/playlist/:id" element={<PlaylistView />} />
                    <Route path="/album/:id" element={<AlbumView />} />
                    <Route path="/profile" element={<UserProfile />} />
                    <Route path="/genres" element={<GenresPage />} />
                    <Route path="/focus" element={<FocusMode />} />
                    <Route path="/mood-playlists" element={<MoodPlaylistGenerator />} />
                    <Route path="/social" element={<SocialFeatures />} />
                    <Route path="/lyrics" element={<LyricsDisplay className="m-6" />} />
                    <Route path="/visualizer" element={
                      <div className="p-6">
                        <h1 className="text-3xl font-bold mb-6">Audio Visualizer</h1>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <h2 className="text-xl font-semibold">Bars</h2>
                            <AudioVisualizer type="bars" width={400} height={200} />
                          </div>
                          <div className="space-y-4">
                            <h2 className="text-xl font-semibold">Wave</h2>
                            <AudioVisualizer type="wave" width={400} height={200} />
                          </div>
                          <div className="space-y-4">
                            <h2 className="text-xl font-semibold">Circular</h2>
                            <AudioVisualizer type="circular" width={400} height={400} />
                          </div>
                        </div>
                      </div>
                    } />
                    <Route path="/liked" element={<div className="p-6"><h1 className="text-2xl font-bold">Liked Songs</h1><p className="text-muted-foreground">Liked songs coming soon...</p></div>} />
                    <Route path="/create-playlist" element={<div className="p-6"><h1 className="text-2xl font-bold">Create Playlist</h1><p className="text-muted-foreground">Playlist creation coming soon...</p></div>} />
                    <Route path="/downloaded" element={<div className="p-6"><h1 className="text-2xl font-bold">Downloaded Music</h1><p className="text-muted-foreground">Downloaded music coming soon...</p></div>} />
                  </Routes>
                </main>
              </div>
            </div>
            
            {/* Mini Player */}
            <MiniPlayer />
          </div>
        </Router>
      </PlayerProvider>
    </ThemeProvider>
  );
}

export default App;

