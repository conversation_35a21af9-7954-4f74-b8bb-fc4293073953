import React, { useState } from 'react';
import { Plus, Search, Grid3X3, List, Filter, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent } from '@/components/ui/card';
import { PlaylistCard } from '@/components/UI/PlaylistCard';
import { AlbumCard } from '@/components/UI/AlbumCard';
import { TrackCard } from '@/components/UI/TrackCard';
import { mockPlaylists, mockAlbums, mockTracks } from '@/utils/mockData';

export function LibraryPage() {
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('recent'); // 'recent', 'name', 'creator', 'added'

  // Mock user's library data
  const userPlaylists = mockPlaylists.filter(playlist => playlist.isOwner || playlist.creator === 'John Doe');
  const likedSongs = mockTracks.slice(0, 3);
  const followedAlbums = mockAlbums.slice(0, 2);

  const handleCreatePlaylist = () => {
    console.log('Creating new playlist...');
    // In a real app, this would open a modal or navigate to create playlist page
  };

  const handlePlayPlaylist = (playlist) => {
    console.log('Playing playlist:', playlist);
  };

  const handlePlayAlbum = (album) => {
    console.log('Playing album:', album);
  };

  const handlePlayTrack = (track) => {
    console.log('Playing track:', track);
  };

  const filteredPlaylists = userPlaylists.filter(playlist =>
    playlist.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    playlist.creator.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Your Library</h1>
        <Button onClick={handleCreatePlaylist} className="bg-green-500 hover:bg-green-600">
          <Plus className="h-4 w-4 mr-2" />
          Create Playlist
        </Button>
      </div>

      {/* Search and Controls */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search in Your Library"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-muted/50 border-none focus:bg-background"
          />
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center gap-1 bg-muted rounded-lg p-1">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('grid')}
            className="h-8 w-8 p-0"
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
            className="h-8 w-8 p-0"
          >
            <List className="h-4 w-4" />
          </Button>
        </div>

        {/* Sort Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Sort
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setSortBy('recent')}>
              Recently Added
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSortBy('name')}>
              Alphabetical
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSortBy('creator')}>
              Creator
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Library Content */}
      <Tabs defaultValue="all" className="w-full">
        <TabsList>
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="playlists">Playlists</TabsTrigger>
          <TabsTrigger value="albums">Albums</TabsTrigger>
          <TabsTrigger value="artists">Artists</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          {/* Quick Access */}
          <section>
            <h2 className="text-xl font-bold mb-4">Quick Access</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Liked Songs */}
              <Card className="group hover:bg-muted/50 transition-colors cursor-pointer">
                <CardContent className="p-4 flex items-center gap-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-400 rounded-lg flex items-center justify-center relative">
                    <span className="text-white text-2xl">♥</span>
                    <Button
                      size="sm"
                      className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full bg-green-500 hover:bg-green-600 opacity-0 group-hover:opacity-100 transition-opacity p-0"
                    >
                      <Play className="h-4 w-4 ml-0.5" />
                    </Button>
                  </div>
                  <div>
                    <h3 className="font-semibold">Liked Songs</h3>
                    <p className="text-sm text-muted-foreground">{likedSongs.length} songs</p>
                  </div>
                </CardContent>
              </Card>

              {/* Downloaded Music */}
              <Card className="group hover:bg-muted/50 transition-colors cursor-pointer">
                <CardContent className="p-4 flex items-center gap-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-400 rounded-lg flex items-center justify-center relative">
                    <span className="text-white text-2xl">↓</span>
                    <Button
                      size="sm"
                      className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full bg-green-500 hover:bg-green-600 opacity-0 group-hover:opacity-100 transition-opacity p-0"
                    >
                      <Play className="h-4 w-4 ml-0.5" />
                    </Button>
                  </div>
                  <div>
                    <h3 className="font-semibold">Downloaded</h3>
                    <p className="text-sm text-muted-foreground">12 albums</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Recently Played */}
          <section>
            <h2 className="text-xl font-bold mb-4">Recently Played</h2>
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                {filteredPlaylists.slice(0, 6).map((playlist) => (
                  <PlaylistCard
                    key={playlist.id}
                    playlist={playlist}
                    onPlay={handlePlayPlaylist}
                  />
                ))}
              </div>
            ) : (
              <div className="space-y-2">
                {filteredPlaylists.map((playlist) => (
                  <Card key={playlist.id} className="group hover:bg-muted/50 transition-colors cursor-pointer">
                    <CardContent className="p-3 flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-400 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm">♪</span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium truncate">{playlist.name}</h3>
                        <p className="text-sm text-muted-foreground truncate">
                          Playlist • {playlist.creator}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePlayPlaylist(playlist)}
                        className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0"
                      >
                        <Play className="h-4 w-4" />
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </section>
        </TabsContent>

        <TabsContent value="playlists">
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
              {filteredPlaylists.map((playlist) => (
                <PlaylistCard
                  key={playlist.id}
                  playlist={playlist}
                  onPlay={handlePlayPlaylist}
                />
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredPlaylists.map((playlist) => (
                <Card key={playlist.id} className="group hover:bg-muted/50 transition-colors cursor-pointer">
                  <CardContent className="p-3 flex items-center gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-400 rounded-md" />
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium truncate">{playlist.name}</h3>
                      <p className="text-sm text-muted-foreground truncate">
                        {playlist.trackCount} songs • {playlist.creator}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePlayPlaylist(playlist)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0"
                    >
                      <Play className="h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="albums">
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
              {followedAlbums.map((album) => (
                <AlbumCard
                  key={album.id}
                  album={album}
                  onPlay={handlePlayAlbum}
                />
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {followedAlbums.map((album) => (
                <Card key={album.id} className="group hover:bg-muted/50 transition-colors cursor-pointer">
                  <CardContent className="p-3 flex items-center gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-400 rounded-md" />
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium truncate">{album.title}</h3>
                      <p className="text-sm text-muted-foreground truncate">
                        Album • {album.artist}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePlayAlbum(album)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0"
                    >
                      <Play className="h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="artists">
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold mb-2">No followed artists yet</h2>
            <p className="text-muted-foreground">Follow your favorite artists to see them here</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

